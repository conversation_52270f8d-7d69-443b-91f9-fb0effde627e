<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DirectoryTree 样式测试</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: #f5f5f5;
        }
        
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .comparison-container {
            display: flex;
            gap: 20px;
            margin-top: 20px;
        }
        
        .tree-panel {
            flex: 1;
            border: 1px solid #dcdfe6;
            border-radius: 6px;
            overflow: hidden;
        }
        
        .tree-header {
            background: #f8f9fa;
            padding: 12px 16px;
            border-bottom: 1px solid #dcdfe6;
            font-weight: 600;
            color: #333;
        }
        
        .tree-content {
            padding: 16px;
            height: 400px;
            overflow: auto;
        }
        
        /* 模拟 WikiContent.vue 中的 a-tree 样式 */
        .wiki-style .tree-node {
            margin-bottom: 0;
            border-radius: 6px;
            transition: background-color 0.3s;
            padding: 4px 8px;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 8px;
            line-height: 32px;
        }
        
        .wiki-style .tree-node:hover {
            background-color: #ebf1fe !important;
        }
        
        .wiki-style .tree-node.selected {
            background-color: #ebf1fe !important;
        }
        
        .wiki-style .tree-node .node-icon {
            color: #666666;
            font-size: 14px;
            transition: transform 0.3s ease;
        }
        
        .wiki-style .tree-node .node-icon.expanded {
            transform: rotate(90deg);
        }
        
        .wiki-style .tree-node .node-content {
            flex: 1;
            display: flex;
            align-items: center;
            gap: 6px;
        }
        
        .wiki-style .tree-node .node-actions {
            opacity: 0;
            transition: opacity 0.2s;
        }
        
        .wiki-style .tree-node:hover .node-actions {
            opacity: 1;
        }
        
        /* 模拟 DirectoryTree.vue 修改前的样式 */
        .old-style .tree-node {
            padding: 4px 8px;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 8px;
            line-height: 28px;
            border-radius: 4px;
        }
        
        .old-style .tree-node:hover {
            background-color: #e6f7ff;
        }
        
        .old-style .tree-node.selected {
            background-color: #bae7ff;
        }
        
        .old-style .tree-node .node-icon {
            color: #999;
            font-size: 14px;
        }
        
        .old-style .tree-node .node-content {
            flex: 1;
            display: flex;
            align-items: center;
            gap: 6px;
        }
        
        .old-style .tree-node .node-actions {
            opacity: 0;
            transition: opacity 0.2s;
        }
        
        .old-style .tree-node:hover .node-actions {
            opacity: 1;
        }
        
        .folder-icon {
            width: 16px;
            height: 16px;
            background: #ffa500;
            border-radius: 2px;
            position: relative;
        }
        
        .folder-icon::before {
            content: '';
            position: absolute;
            top: -2px;
            left: 2px;
            width: 6px;
            height: 2px;
            background: #ffa500;
            border-radius: 1px 1px 0 0;
        }
        
        .more-icon {
            width: 16px;
            height: 16px;
            background: #999;
            border-radius: 50%;
            position: relative;
        }
        
        .more-icon::before {
            content: '⋯';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            font-size: 10px;
        }
        
        .indent-1 { margin-left: 16px; }
        .indent-2 { margin-left: 32px; }
        .indent-3 { margin-left: 48px; }
        
        .test-info {
            background: #f0f9ff;
            padding: 16px;
            border-radius: 6px;
            margin-bottom: 20px;
            border-left: 4px solid #0ea5e9;
        }
        
        .test-info h3 {
            margin: 0 0 8px 0;
            color: #0369a1;
        }
        
        .test-info p {
            margin: 0;
            color: #0c4a6e;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>DirectoryTree 样式对比测试</h1>
        
        <div class="test-info">
            <h3>测试说明</h3>
            <p>左侧展示修改后的样式（参考 WikiContent.vue），右侧展示修改前的样式。请对比节点的悬停效果、选中效果和整体视觉一致性。</p>
        </div>
        
        <div class="comparison-container">
            <!-- 修改后的样式 (WikiContent.vue 风格) -->
            <div class="tree-panel">
                <div class="tree-header">修改后样式 (WikiContent.vue 风格)</div>
                <div class="tree-content wiki-style">
                    <div class="tree-node" onclick="toggleNode(this)">
                        <span class="node-icon">▶</span>
                        <div class="node-content">
                            <div class="folder-icon"></div>
                            <span>根目录</span>
                        </div>
                        <div class="node-actions">
                            <div class="more-icon"></div>
                        </div>
                    </div>
                    
                    <div class="tree-node indent-1" onclick="toggleNode(this)">
                        <span class="node-icon expanded">▶</span>
                        <div class="node-content">
                            <div class="folder-icon"></div>
                            <span>项目文档</span>
                        </div>
                        <div class="node-actions">
                            <div class="more-icon"></div>
                        </div>
                    </div>
                    
                    <div class="tree-node indent-2 selected">
                        <span class="node-icon"></span>
                        <div class="node-content">
                            <div class="folder-icon"></div>
                            <span>设计文档</span>
                        </div>
                        <div class="node-actions">
                            <div class="more-icon"></div>
                        </div>
                    </div>
                    
                    <div class="tree-node indent-2">
                        <span class="node-icon"></span>
                        <div class="node-content">
                            <div class="folder-icon"></div>
                            <span>技术文档</span>
                        </div>
                        <div class="node-actions">
                            <div class="more-icon"></div>
                        </div>
                    </div>
                    
                    <div class="tree-node indent-1">
                        <span class="node-icon">▶</span>
                        <div class="node-content">
                            <div class="folder-icon"></div>
                            <span>资源文件</span>
                        </div>
                        <div class="node-actions">
                            <div class="more-icon"></div>
                        </div>
                    </div>
                    
                    <div class="tree-node">
                        <span class="node-icon">▶</span>
                        <div class="node-content">
                            <div class="folder-icon"></div>
                            <span>临时文件</span>
                        </div>
                        <div class="node-actions">
                            <div class="more-icon"></div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 修改前的样式 -->
            <div class="tree-panel">
                <div class="tree-header">修改前样式</div>
                <div class="tree-content old-style">
                    <div class="tree-node" onclick="toggleNodeOld(this)">
                        <span class="node-icon">▶</span>
                        <div class="node-content">
                            <div class="folder-icon"></div>
                            <span>根目录</span>
                        </div>
                        <div class="node-actions">
                            <div class="more-icon"></div>
                        </div>
                    </div>
                    
                    <div class="tree-node indent-1" onclick="toggleNodeOld(this)">
                        <span class="node-icon expanded">▶</span>
                        <div class="node-content">
                            <div class="folder-icon"></div>
                            <span>项目文档</span>
                        </div>
                        <div class="node-actions">
                            <div class="more-icon"></div>
                        </div>
                    </div>
                    
                    <div class="tree-node indent-2 selected">
                        <span class="node-icon"></span>
                        <div class="node-content">
                            <div class="folder-icon"></div>
                            <span>设计文档</span>
                        </div>
                        <div class="node-actions">
                            <div class="more-icon"></div>
                        </div>
                    </div>
                    
                    <div class="tree-node indent-2">
                        <span class="node-icon"></span>
                        <div class="node-content">
                            <div class="folder-icon"></div>
                            <span>技术文档</span>
                        </div>
                        <div class="node-actions">
                            <div class="more-icon"></div>
                        </div>
                    </div>
                    
                    <div class="tree-node indent-1">
                        <span class="node-icon">▶</span>
                        <div class="node-content">
                            <div class="folder-icon"></div>
                            <span>资源文件</span>
                        </div>
                        <div class="node-actions">
                            <div class="more-icon"></div>
                        </div>
                    </div>
                    
                    <div class="tree-node">
                        <span class="node-icon">▶</span>
                        <div class="node-content">
                            <div class="folder-icon"></div>
                            <span>临时文件</span>
                        </div>
                        <div class="node-actions">
                            <div class="more-icon"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div style="margin-top: 20px; padding: 16px; background: #f8f9fa; border-radius: 6px;">
            <h3>主要改进点：</h3>
            <ul>
                <li><strong>悬停效果</strong>：使用 #ebf1fe 背景色，与 WikiContent.vue 保持一致</li>
                <li><strong>选中效果</strong>：同样使用 #ebf1fe 背景色，提供统一的视觉反馈</li>
                <li><strong>圆角设计</strong>：节点使用 6px 圆角，更加现代化</li>
                <li><strong>展开图标</strong>：使用 #666666 颜色，添加 7px 上边距</li>
                <li><strong>滚动条样式</strong>：半透明设计，悬停时显示</li>
                <li><strong>行高统一</strong>：32px 行高，与 WikiContent.vue 一致</li>
            </ul>
        </div>
    </div>

    <script>
        function toggleNode(node) {
            const icon = node.querySelector('.node-icon');
            if (icon.textContent === '▶') {
                icon.textContent = '▼';
                icon.classList.add('expanded');
            } else {
                icon.textContent = '▶';
                icon.classList.remove('expanded');
            }
            
            // 切换选中状态
            document.querySelectorAll('.wiki-style .tree-node').forEach(n => n.classList.remove('selected'));
            node.classList.add('selected');
        }
        
        function toggleNodeOld(node) {
            const icon = node.querySelector('.node-icon');
            if (icon.textContent === '▶') {
                icon.textContent = '▼';
                icon.classList.add('expanded');
            } else {
                icon.textContent = '▶';
                icon.classList.remove('expanded');
            }
            
            // 切换选中状态
            document.querySelectorAll('.old-style .tree-node').forEach(n => n.classList.remove('selected'));
            node.classList.add('selected');
        }
    </script>
</body>
</html>
