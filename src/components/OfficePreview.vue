<template>
  <div v-if="state.src && !state.loading" style="height: 100%; width: 100%">
    <vue-office-pdf
      v-if="state.fileType === 'pdf'"
      :src="state.src"
      @rendered="renderedHandler"
      @error="errorHandler"
    />
    <vue-office-docx
      v-if="fileExtensionsForWord.includes(state.fileType)"
      :src="state.src"
      @rendered="rendered"
    />
    <vue-office-excel
      v-if="fileExtensionsForExcel.includes(state.fileType)"
      :src="state.src"
      style="height: 100%; width: 100%"
      @rendered="renderedHandler"
      @error="errorHandler"
      :options="{ xls: true }"
    />
  </div>
  <div v-else-if="state.loading" class="office-preview-loading">
    <div class="loading-text">文件加载中...</div>
  </div>
</template>

<script setup>
  import {
    ref,
    reactive,
    onMounted,
    onUnmounted,
    watch,
    toRefs,
    inject,
  } from 'vue'
  import {
    fileExtensionsForExcel,
    fileExtensionsForWord,
  } from '~/library/components/FileList/fileUtil'
  import VueOfficePdf from '@vue-office/pdf'
  import VueOfficeExcel from '@vue-office/excel'
  import VueOfficeDocx from '@vue-office/docx'
  import '@vue-office/docx/lib/index.css'
  import '@vue-office/excel/lib/index.css'

  const $baseMessage = inject('$baseMessage')

  const props = defineProps({
    fileUrl: {
      type: String,
      default: '',
    },
    fileType: {
      type: String,
      default: 'pdf',
    },
    dialogVisible: {
      type: Boolean,
      default: false,
    },
    previewMode: {
      type: String,
      default: 'url',
      validator: (value) => ['url', 'binary'].includes(value),
    },
  })

  const { fileType, fileUrl, previewMode } = toRefs(props)

  const state = reactive({
    fileType: props.fileType,
    src: null, // 统一的数据源，可以是 URL 或 ArrayBuffer
    loading: false,
    error: null,
  })

  // 获取二进制数据
  const fetchBinaryData = async (url) => {
    try {
      state.loading = true
      state.error = null

      const response = await fetch(url)
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const arrayBuffer = await response.arrayBuffer()
      state.src = arrayBuffer
      state.loading = false
    } catch (error) {
      console.error('获取二进制数据失败:', error)
      state.error = error.message
      state.loading = false
      $baseMessage('文件加载失败', 'error', 'vab-hey-message-error')
    }
  }

  // 初始化数据源
  const initDataSource = async () => {
    if (!fileUrl.value) {
      state.src = null
      return
    }

    if (previewMode.value === 'url') {
      // URL 模式：直接使用文件 URL
      state.src = fileUrl.value
      state.loading = false
    } else if (previewMode.value === 'binary') {
      // 二进制模式：获取 ArrayBuffer
      await fetchBinaryData(fileUrl.value)
    }
  }

  const showEdit = () => {
    console.log('showEdit')
    state.fileType = fileType.value
    initDataSource()
  }

  function renderedHandler() {
    console.log('渲染完成，模式:', props.previewMode)
  }

  function errorHandler(e) {
    console.log('渲染失败', e)
    $baseMessage('文件已损坏或者受到保护', 'warning', 'vab-hey-message-warning')
  }

  const rendered = () => {
    console.log('渲染完成')
  }

  const initState = () => {
    state.fileType = fileType.value
    initDataSource()
  }

  // 监听 props 变化
  watch([fileUrl, fileType, previewMode], () => {
    initState()
  })

  onMounted(() => {
    console.log('onMounted')
    initState()
    console.log(props)
  })

  onUnmounted(() => {
    console.log('onUnmounted')
  })

  defineExpose({
    showEdit,
  })
</script>

<style scoped>
  .office-preview-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    width: 100%;
  }

  .loading-text {
    font-size: 14px;
    color: #666;
  }
</style>
