/**
 * FMS 文件管理 API
 */

import request from '@/utils/request'

const BASE_URL = '/api/fms/files'

/**
 * 获取文件列表
 * @param {Object} params 查询参数
 * @returns {Promise}
 */
export const list = (params = {}) => {
  return request({
    url: BASE_URL,
    method: 'GET',
    params,
  })
}

/**
 * 获取单个文件信息
 * @param {number} id 文件ID
 * @returns {Promise}
 */
export const show = (id) => {
  return request({
    url: `${BASE_URL}/${id}`,
    method: 'GET',
  })
}

/**
 * 上传文件
 * @param {Object} data 上传数据
 * @param {Function} onProgress 进度回调
 * @returns {Promise}
 */
export const upload = (data, onProgress) => {
  const formData = new FormData()

  // 添加文件
  formData.append('file', data.file)

  // 添加其他参数
  if (data.directoryId) formData.append('directory_id', data.directoryId)
  if (data.name) formData.append('name', data.name)

  return request({
    url: BASE_URL,
    method: 'POST',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
    onUploadProgress: (progressEvent) => {
      if (onProgress) {
        const percent = Math.round(
          (progressEvent.loaded * 100) / progressEvent.total
        )
        onProgress({
          loaded: progressEvent.loaded,
          total: progressEvent.total,
          percent: percent,
        })
      }
    },
  })
}

/**
 * 下载文件
 * @param {number} id 文件ID
 * @param {number} version 版本号（可选）
 * @returns {Promise}
 */
export const download = (id, version) => {
  const params = {}
  if (version) params.version = version

  return request({
    url: `${BASE_URL}/${id}/download`,
    method: 'GET',
    params,
    responseType: 'blob',
  }).then((response) => {
    // 从响应头获取文件名
    const contentDisposition = response.headers['content-disposition']
    let filename = `file_${id}`

    if (contentDisposition) {
      const filenameMatch = contentDisposition.match(
        /filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/
      )
      if (filenameMatch && filenameMatch[1]) {
        filename = filenameMatch[1].replace(/['"]/g, '')
      }
    }

    // 创建下载URL
    const url = window.URL.createObjectURL(new Blob([response.data]))

    return {
      url,
      filename,
      size: response.data.size,
      mimeType: response.headers['content-type'],
    }
  })
}

/**
 * 更新文件信息
 * @param {number} id 文件ID
 * @param {Object} data 更新数据
 * @returns {Promise}
 */
export const update = (id, data) => {
  return request({
    url: `${BASE_URL}/${id}`,
    method: 'PUT',
    data,
  })
}

/**
 * 删除文件（软删除）
 * @param {number} id 文件ID
 * @returns {Promise}
 */
export const destroy = (id) => {
  return request({
    url: `${BASE_URL}/${id}`,
    method: 'DELETE',
  })
}

/**
 * 移动文件到目录
 * @param {number} id 文件ID
 * @param {Object} data 移动数据
 * @returns {Promise}
 */
export const move = (id, data) => {
  return request({
    url: `${BASE_URL}/${id}/move`,
    method: 'PUT',
    data,
  })
}

/**
 * 复制文件
 * @param {number} id 文件ID
 * @param {Object} data 复制数据
 * @returns {Promise}
 */
export const copy = (id, data) => {
  return request({
    url: `${BASE_URL}/${id}/copy`,
    method: 'POST',
    data,
  })
}

/**
 * 获取文件预览URL
 * @param {number} id 文件ID
 * @returns {Promise}
 */
export const getPreviewUrl = (id) => {
  return request({
    url: `${BASE_URL}/${id}/preview-url`,
    method: 'GET',
  })
}

/**
 * 预览文件内容
 * @param {number} id 文件ID
 * @returns {Promise}
 */
export const preview = (id) => {
  return request({
    url: `${BASE_URL}/${id}/preview`,
    method: 'GET',
  })
}

/**
 * 批量删除文件
 * @param {Array} fileIds 文件ID数组
 * @returns {Promise}
 */
export const batchDelete = (fileIds) => {
  return request({
    url: `${BASE_URL}/batch/delete`,
    method: 'POST',
    data: {
      file_ids: fileIds,
    },
  })
}

/**
 * 批量移动文件
 * @param {Array} fileIds 文件ID数组
 * @param {number} directoryId 目标目录ID
 * @returns {Promise}
 */
export const batchMove = (fileIds, directoryId) => {
  return request({
    url: `${BASE_URL}/batch/move`,
    method: 'POST',
    data: {
      file_ids: fileIds,
      directory_id: directoryId,
    },
  })
}
