/**
 * FMS 目录管理 API
 */

import request from '@/utils/request'

const BASE_URL = '/api/fms/directories'

/**
 * 获取目录列表
 * @param {Object} params 查询参数
 * @returns {Promise}
 */
export const list = (params = {}) => {
  return request({
    url: BASE_URL,
    method: 'GET',
    params,
  })
}

/**
 * 获取目录树
 * @param {Object} params 查询参数
 * @returns {Promise}
 */
export const tree = (params = {}) => {
  return request({
    url: `${BASE_URL}/tree`,
    method: 'GET',
    params,
  })
}

/**
 * 获取单个目录信息
 * @param {number} id 目录ID
 * @returns {Promise}
 */
export const overview = (id) => {
  return request({
    url: `${BASE_URL}/${id}`,
    method: 'GET',
  })
}

/**
 * 创建目录
 * @param {Object} data 目录数据
 * @param {number|null} data.parentId 父目录ID
 * @param {string} data.name 目录名称
 * @param {string} data.visibility 可见性
 * @param {number} data.sortOrder 排序
 * @returns {Promise}
 */
export const create = (data) => {
  return request({
    url: BASE_URL,
    method: 'POST',
    data,
  })
}

/**
 * 重命名目录
 * @param {number} id 目录ID
 * @param {Object} data 更新数据
 * @param {string} data.name 新名称
 * @returns {Promise}
 */
export const rename = (id, data) => {
  return request({
    url: `${BASE_URL}/${id}/rename`,
    method: 'PUT',
    data,
  })
}

/**
 * 移动目录
 * @param {number} id 目录ID
 * @param {Object} data 移动数据
 * @param {number|null} data.newParentId 新父目录ID
 * @returns {Promise}
 */
export const move = (id, data) => {
  return request({
    url: `${BASE_URL}/${id}/move`,
    method: 'PUT',
    data,
  })
}

/**
 * 更新目录
 * @param {number} id 目录ID
 * @param {Object} data 更新数据
 * @param {string} data.name 目录名称
 * @param {string} data.description 目录描述
 * @param {string} data.visibility 可见性
 * @param {number} data.sort_order 排序
 * @returns {Promise}
 */
export const update = (id, data) => {
  return request({
    url: `${BASE_URL}/${id}`,
    method: 'PUT',
    data,
  })
}

/**
 * 删除目录（软删除）
 * @param {number} id 目录ID
 * @returns {Promise}
 */
export const destroy = (id) => {
  return request({
    url: `${BASE_URL}/${id}`,
    method: 'DELETE',
  })
}

/**
 * 批量删除目录
 * @param {Array} directoryIds 目录ID数组
 * @returns {Promise}
 */
export const batchDelete = (directoryIds) => {
  return request({
    url: `${BASE_URL}/batch/delete`,
    method: 'POST',
    data: {
      directory_ids: directoryIds,
    },
  })
}

/**
 * 批量移动目录
 * @param {Array} directoryIds 目录ID数组
 * @param {number|null} parentId 目标父目录ID
 * @returns {Promise}
 */
export const batchMove = (directoryIds, parentId) => {
  return request({
    url: `${BASE_URL}/batch/move`,
    method: 'POST',
    data: {
      directory_ids: directoryIds,
      parent_id: parentId,
    },
  })
}

/**
 * 目录拖拽重排序
 * @param {Object} payload 拖拽排序参数
 * @param {number} payload.drag_node_id 拖拽节点ID
 * @param {Object} payload.target_node 目标节点信息
 * @param {boolean} payload.drop_to_gap 是否拖拽到间隙位置
 * @param {number} payload.drop_position 拖拽位置 (-1/0/1)
 * @returns {Promise}
 */
export const rearrangeDirectories = (payload) => {
  return request({
    url: `${BASE_URL}/rearrange`,
    method: 'POST',
    data: payload,
  })
}
