<template>
  <div class="unified-file-list">
    <!-- 高级搜索筛选面板 - 弹出式布局 -->

    <vab-query-form>
      <vab-query-form-left-panel :span="12">
        <div class="unified-file-list__advanced-search-wrapper">
          <el-popover
            ref="advancedSearchPopoverRef"
            placement="bottom-start"
            :width="400"
            trigger="click"
            :hide-after="0"
            popper-class="unified-file-list__advanced-search-popover"
          >
            <template #reference>
              <el-button type="primary" :icon="Search" plain>
                高级搜索
              </el-button>
            </template>

            <AdvancedSearchPanel
              ref="advancedSearchRef"
              :searching="loading"
              :default-expanded="true"
              @search="handleAdvancedSearchWithClose"
              @reset="handleSearchResetWithClose"
            />
          </el-popover>
        </div>
        <el-button type="default" :icon="Refresh" @click="handleRefresh">
          刷新
        </el-button>
      </vab-query-form-left-panel>
      <vab-query-form-right-panel :span="12">
        <el-button
          type="primary"
          :icon="Upload"
          @click="handleUpload"
          v-if="props.directoryId"
        >
          上传文件
        </el-button>
      </vab-query-form-right-panel>
    </vab-query-form>
    <!-- 批量操作工具栏 -->
    <div
      v-if="selectedItems.length > 0"
      class="unified-file-list__batch-toolbar"
    >
      <div class="unified-file-list__batch-info">
        已选择 {{ selectedItems.length }} 项
      </div>
      <div class="unified-file-list__batch-actions">
        <el-button
          type="primary"
          size="small"
          :icon="Download"
          @click="handleBatchDownload"
          :disabled="!canBatchDownload"
        >
          批量下载
        </el-button>
        <el-button
          type="warning"
          size="small"
          :icon="FolderOpened"
          @click="handleBatchMove"
        >
          批量移动
        </el-button>
        <el-button
          type="danger"
          size="small"
          :icon="Delete"
          @click="handleBatchDelete"
        >
          批量删除
        </el-button>
        <el-button type="text" size="small" @click="clearSelection">
          取消选择
        </el-button>
      </div>
    </div>

    <!-- 拖拽上传遮罩 -->
    <div
      v-if="dragOver"
      class="unified-file-list__drag-overlay"
      @drop.prevent="handleDrop"
      @dragover.prevent
      @dragleave="handleDragLeave"
    >
      <div class="unified-file-list__drag-content">
        <el-icon class="unified-file-list__drag-icon">
          <UploadFilled />
        </el-icon>
        <div class="unified-file-list__drag-text">释放文件以上传到当前目录</div>
      </div>
    </div>

    <el-table
      v-loading="loading"
      :data="computedTableData"
      @selection-change="handleSelectionChange"
      @sort-change="handleSortChange"
      stripe
      style="width: 100%; height: calc(100% - 110px)"
      ref="tableRef"
      class="unified-file-list__table"
      @dragover.prevent="handleDragOver"
      @drop.prevent="handleDrop"
      @dragleave="handleDragLeave"
    >
      <el-table-column type="selection" width="55" />

      <el-table-column
        label="名称"
        min-width="300"
        prop="name"
        sortable="custom"
        :sort-orders="['ascending', 'descending']"
      >
        <template #default="{ row }">
          <!-- 文件夹显示 -->
          <div v-if="row.item_type === 'directory'" class="unified-file-item">
            <!-- <el-icon class="unified-file-icon" :size="20">
              <FolderOpened />
            </el-icon> -->
            <vab-icon
              :icon="'icon-dir'"
              is-custom-svg
              :style="{
                width: 18 + 'px',
                height: 18 + 'px',
                marginRight: '0px',
              }"
            />
            <span
              class="unified-row-name"
              @click="handleDirRowClick(row)"
              style="cursor: pointer"
            >
              {{ row.name }}
            </span>
          </div>

          <!-- 文件显示 - 使用新的 FileNameCell 组件 -->
          <FileNameCell
            v-else
            :file="row"
            :clickable="true"
            :show-size="false"
            :show-thumbnail="true"
            @preview="handlePreview"
          />
        </template>
      </el-table-column>

      <el-table-column
        label="大小"
        width="120"
        prop="size"
        sortable="custom"
        :sort-orders="['ascending', 'descending']"
      >
        <template #default="{ row }">
          <span v-if="row.item_type === 'directory'">-</span>
          <span v-else>{{ formatFileSize(row.size) }}</span>
        </template>
      </el-table-column>

      <el-table-column
        label="修改时间"
        width="180"
        prop="updated_at"
        sortable="custom"
        :sort-orders="['ascending', 'descending']"
      >
        <template #default="{ row }">
          {{ formatDateTime(row.updated_at) }}
        </template>
      </el-table-column>

      <el-table-column label="操作" width="60" fixed="right">
        <template #default="{ row }">
          <div
            class="unified-file-list__actions"
            @click.stop=""
            @dblclick.stop=""
          >
            <!-- 文件夹操作 -->
            <el-dropdown v-if="row.item_type === 'directory'" trigger="click">
              <vab-icon
                style="
                  width: 24px;
                  height: 24px;
                  margin-top: -4px;
                  margin-bottom: -2px;
                "
                class="unified-file-list__more-icon"
                is-custom-svg
                icon="more"
              />
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item @click="handleDirRowClick(row)">
                    <el-icon><FolderOpened /></el-icon>
                    进入文件夹
                  </el-dropdown-item>
                  <el-dropdown-item @click="handleRename(row)">
                    重命名
                  </el-dropdown-item>
                  <el-dropdown-item @click="handleMove(row)">
                    移动
                  </el-dropdown-item>
                  <el-dropdown-item @click="handleDelete(row)" divided>
                    <el-icon color="#ff4d4f"><Delete /></el-icon>
                    <span style="color: #ff4d4f">删除</span>
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>

            <!-- 文件操作 -->
            <el-dropdown v-else trigger="click">
              <vab-icon
                style="
                  width: 24px;
                  height: 24px;
                  margin-top: -4px;
                  margin-bottom: -2px;
                "
                class="unified-file-list__more-icon"
                is-custom-svg
                icon="more"
              />
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item @click="handlePreview(row)">
                    <el-icon><View /></el-icon>
                    预览
                  </el-dropdown-item>
                  <el-dropdown-item @click="handleDownload(row)">
                    <el-icon><Download /></el-icon>
                    下载
                  </el-dropdown-item>
                  <el-dropdown-item @click="handleRename(row)">
                    重命名
                  </el-dropdown-item>
                  <el-dropdown-item @click="handleMove(row)">
                    移动
                  </el-dropdown-item>
                  <el-dropdown-item @click="handleDelete(row)" divided>
                    <el-icon color="#ff4d4f"><Delete /></el-icon>
                    <span style="color: #ff4d4f">删除</span>
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <!-- 统计信息和分页 -->
    <div class="unified-file-list-footer-wrapper" v-if="total > 0">
      <div class="unified-file-list-pagination-wrapper">
        <el-pagination
          background
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :total="total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
      <div class="unified-file-list-stats-info">
        <span class="unified-file-list-stats-item">
          文件夹: {{ directoryCount }}
        </span>
        <span class="unified-file-list-stats-item">文件: {{ fileCount }}</span>
        <span class="unified-file-list-stats-item">总计: {{ total }}</span>
      </div>
    </div>
  </div>
</template>

<script setup>
  import { ref, computed, onMounted, watch, inject } from 'vue'
  import {
    FolderOpened,
    Document,
    Picture,
    VideoPlay,
    Headset,
    Files,
    Upload,
    UploadFilled,
    Refresh,
    View,
    Download,
    Delete,
    ArrowDown,
    Search,
  } from '@element-plus/icons-vue'
  import * as directoryApi from '@/api/fms/directories'
  import * as fileApi from '@/api/fms/files'
  import FileNameCell from './file/FileNameCell.vue'
  import AdvancedSearchPanel from './AdvancedSearchPanel.vue'

  const $baseMessage = inject('$baseMessage')
  const $baseConfirm = inject('$baseConfirm')

  const props = defineProps({
    directoryId: {
      type: [Number, String],
      default: null,
    },
    searchKeyword: {
      type: String,
      default: '',
    },
  })

  const emit = defineEmits([
    'refresh',
    'directory-enter',
    'file-preview',
    'item-rename',
    'item-delete',
    'item-move',
    'upload',
    'batch-move',
    'batch-delete',
  ])

  // 工具栏操作
  const handleUpload = () => {
    emit('upload')
  }

  // 响应式数据
  const loading = ref(false)
  const tableData = ref([])
  const selectedItems = ref([])
  const currentPage = ref(1)
  const pageSize = ref(20)
  const total = ref(0)
  const directoryCount = ref(0)
  const fileCount = ref(0)
  const sortField = ref('updated_at')
  const sortOrder = ref('desc')
  const advancedFilters = ref({})
  const advancedSearchRef = ref(null)
  const advancedSearchPopoverRef = ref(null)
  const tableRef = ref(null)
  const dragOver = ref(false)
  const dragCounter = ref(0)

  // 计算属性
  const computedTableData = computed(() => {
    return tableData.value
  })

  // 是否可以批量下载（只有文件可以下载，目录不支持）
  const canBatchDownload = computed(() => {
    return selectedItems.value.some((item) => item.item_type === 'file')
  })

  const handleRefresh = () => {
    loadData()
  }

  // 方法
  const loadData = async () => {
    loading.value = true
    const start = Date.now()

    try {
      const params = {
        parent_id: props.directoryId,
        search: props.searchKeyword,
        page: currentPage.value,
        page_size: pageSize.value,
        union: 1, // 启用联合查询
        sort: sortField.value,
        order: sortOrder.value,
        ...advancedFilters.value, // 合并高级搜索筛选条件
      }

      const response = await directoryApi.list(params)
      const responseData = response.data || response

      // 处理联合查询返回的数据
      tableData.value = responseData.data || []
      total.value = responseData.total || 0
      directoryCount.value = responseData.directory_count || 0
      fileCount.value = responseData.file_count || 0

      const duration = Date.now() - start
      if (duration < 500) {
        await new Promise((resolve) => setTimeout(resolve, 500 - duration))
      }
    } catch (error) {
      $baseMessage(
        '加载数据失败：' + error.message,
        'error',
        'vab-hey-message-error'
      )
    } finally {
      loading.value = false
    }
  }

  // 事件处理
  const handleDirRowClick = (row) => {
    if (row.item_type === 'directory') {
      emit('directory-enter', row)
    } else {
      emit('file-preview', row)
    }
  }

  const handleSelectionChange = (selection) => {
    selectedItems.value = selection
  }

  const handleDownload = async (row) => {
    try {
      const result = await fileApi.download(row.id)
      const link = document.createElement('a')
      link.href = result.url
      link.download = result.filename
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(result.url)
    } catch (error) {
      $baseMessage(
        '下载失败：' + error.message,
        'error',
        'vab-hey-message-error'
      )
    }
  }

  const handlePreview = (row) => {
    emit('file-preview', row)
  }

  const handleRename = (row) => {
    emit('item-rename', row)
  }

  const handleMove = (row) => {
    emit('item-move', row)
  }

  const handleDelete = async (row) => {
    $baseConfirm(
      `确定要删除${row.item_type === 'directory' ? '文件夹' : '文件'} "${
        row.name
      }" 吗？`,
      null,
      async () => {
        try {
          if (row.item_type === 'directory') {
            await directoryApi.destroy(row.id)
          } else {
            await fileApi.destroy(row.id)
          }

          $baseMessage('删除成功', 'success', 'vab-hey-message-success')
          emit('item-delete', row)
          loadData()
        } catch (error) {
          $baseMessage(
            '删除失败：' + error.message,
            'error',
            'vab-hey-message-error'
          )
        }
      }
    )
  }

  const handleSizeChange = (size) => {
    pageSize.value = size
    loadData()
  }

  const handleCurrentChange = (page) => {
    currentPage.value = page
    loadData()
  }

  // 排序处理
  const handleSortChange = ({ prop, order }) => {
    if (prop) {
      sortField.value = prop
      // Element Plus 的排序顺序转换
      if (order === 'ascending') {
        sortOrder.value = 'asc'
      } else if (order === 'descending') {
        sortOrder.value = 'desc'
      } else {
        // 取消排序，恢复默认
        sortField.value = 'updated_at'
        sortOrder.value = 'desc'
      }

      // 重置到第一页并重新加载数据
      currentPage.value = 1
      loadData()
    }
  }

  // 高级搜索处理
  const handleAdvancedSearch = (searchParams) => {
    advancedFilters.value = searchParams
    currentPage.value = 1 // 重置到第一页
    loadData()
  }

  // 搜索重置处理
  const handleSearchReset = () => {
    advancedFilters.value = {}
    currentPage.value = 1 // 重置到第一页
    loadData()
  }

  // 高级搜索处理（带关闭弹窗）
  const handleAdvancedSearchWithClose = (searchParams) => {
    handleAdvancedSearch(searchParams)
    // 关闭弹窗
    if (advancedSearchPopoverRef.value) {
      advancedSearchPopoverRef.value.hide()
    }
  }

  // 搜索重置处理（带关闭弹窗）
  const handleSearchResetWithClose = () => {
    handleSearchReset()
    // 关闭弹窗
    if (advancedSearchPopoverRef.value) {
      advancedSearchPopoverRef.value.hide()
    }
  }

  // 批量操作相关方法
  const clearSelection = () => {
    if (tableRef.value) {
      tableRef.value.clearSelection()
    }
  }

  // 批量下载
  const handleBatchDownload = async () => {
    const filesToDownload = selectedItems.value.filter(
      (item) => item.item_type === 'file'
    )

    if (filesToDownload.length === 0) {
      $baseMessage('请选择要下载的文件', 'warning', 'vab-hey-message-warning')
      return
    }

    if (filesToDownload.length === 1) {
      // 单个文件直接下载
      await handleDownload(filesToDownload[0])
    } else {
      // 多个文件批量下载
      $baseMessage('正在准备下载文件...', 'info', 'vab-hey-message-info')

      for (const file of filesToDownload) {
        try {
          await handleDownload(file)
          // 添加延迟避免浏览器阻止多个下载
          await new Promise((resolve) => setTimeout(resolve, 500))
        } catch (error) {
          $baseMessage(
            `下载文件 "${file.name}" 失败：${error.message}`,
            'error',
            'vab-hey-message-error'
          )
        }
      }

      $baseMessage(
        `已开始下载 ${filesToDownload.length} 个文件`,
        'success',
        'vab-hey-message-success'
      )
    }
  }

  // 批量移动
  const handleBatchMove = () => {
    if (selectedItems.value.length === 0) {
      $baseMessage('请选择要移动的项目', 'warning', 'vab-hey-message-warning')
      return
    }

    // 触发批量移动事件，由父组件处理移动对话框
    emit('batch-move', selectedItems.value)
  }

  // 批量删除
  const handleBatchDelete = () => {
    if (selectedItems.value.length === 0) {
      $baseMessage('请选择要删除的项目', 'warning', 'vab-hey-message-warning')
      return
    }

    const fileCount = selectedItems.value.filter(
      (item) => item.item_type === 'file'
    ).length
    const dirCount = selectedItems.value.filter(
      (item) => item.item_type === 'directory'
    ).length

    let message = '确定要删除选中的'
    if (fileCount > 0 && dirCount > 0) {
      message += ` ${dirCount} 个文件夹和 ${fileCount} 个文件`
    } else if (fileCount > 0) {
      message += ` ${fileCount} 个文件`
    } else {
      message += ` ${dirCount} 个文件夹`
    }
    message += ' 吗？'

    $baseConfirm(message, null, async () => {
      await performBatchDelete()
    })
  }

  // 执行批量删除
  const performBatchDelete = async () => {
    // 分离文件和目录
    const files = selectedItems.value.filter(
      (item) => item.item_type === 'file'
    )
    const directories = selectedItems.value.filter(
      (item) => item.item_type === 'directory'
    )

    const results = []

    try {
      // 批量删除文件
      if (files.length > 0) {
        const fileIds = files.map((file) => file.id)
        const fileResult = await fileApi.batchDelete(fileIds)
        results.push(...fileResult.results)
      }

      // 批量删除目录
      if (directories.length > 0) {
        const directoryIds = directories.map((dir) => dir.id)
        const dirResult = await directoryApi.batchDelete(directoryIds)
        results.push(...dirResult.results)
      }

      const successCount = results.filter((r) => r.success).length
      const failCount = results.filter((r) => !r.success).length

      if (successCount > 0) {
        $baseMessage(
          `成功删除 ${successCount} 项${
            failCount > 0 ? `，失败 ${failCount} 项` : ''
          }`,
          successCount === selectedItems.value.length ? 'success' : 'warning',
          successCount === selectedItems.value.length
            ? 'vab-hey-message-success'
            : 'vab-hey-message-warning'
        )

        // 清空选择并刷新数据
        clearSelection()
        loadData()
      } else {
        $baseMessage('删除失败', 'error', 'vab-hey-message-error')
      }
    } catch (error) {
      $baseMessage(
        '批量删除失败：' + error.message,
        'error',
        'vab-hey-message-error'
      )
    }
  }

  // 工具函数
  const formatFileSize = (bytes) => {
    if (!bytes) return '0 B'
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const formatDateTime = (dateTime) => {
    if (!dateTime) return '-'
    return dateTime
  }

  // 拖拽上传相关方法
  const handleDragOver = (e) => {
    e.preventDefault()
    e.stopPropagation()

    // 检查是否包含文件
    if (e.dataTransfer.types.includes('Files')) {
      dragCounter.value++
      dragOver.value = true
    }
  }

  const handleDragLeave = (e) => {
    e.preventDefault()
    e.stopPropagation()

    dragCounter.value--
    if (dragCounter.value <= 0) {
      dragCounter.value = 0
      dragOver.value = false
    }
  }

  const handleDrop = async (e) => {
    e.preventDefault()
    e.stopPropagation()

    dragOver.value = false
    dragCounter.value = 0

    const files = Array.from(e.dataTransfer.files)
    if (files.length === 0) {
      return
    }

    // 检查文件大小
    const maxSize = 100 * 1024 * 1024 // 100MB
    const oversizedFiles = files.filter((file) => file.size > maxSize)
    if (oversizedFiles.length > 0) {
      $baseMessage(
        `以下文件超过100MB限制：${oversizedFiles
          .map((f) => f.name)
          .join(', ')}`,
        'error',
        'vab-hey-message-error'
      )
      return
    }

    // 开始上传文件
    await uploadFiles(files)
  }

  // 上传文件方法
  const uploadFiles = async (files) => {
    if (!props.directoryId && props.directoryId !== 0) {
      $baseMessage('请先选择目录', 'warning', 'vab-hey-message-warning')
      return
    }

    $baseMessage(
      `开始上传 ${files.length} 个文件...`,
      'info',
      'vab-hey-message-info'
    )

    let successCount = 0
    let failCount = 0

    for (const file of files) {
      try {
        const formData = new FormData()
        formData.append('file', file)
        formData.append('directory_id', props.directoryId)
        formData.append('visibility', 'department') // 默认部门可见

        await fileApi.upload(formData)
        successCount++
      } catch (error) {
        failCount++
        console.error(`上传文件 ${file.name} 失败:`, error)
      }
    }

    // 显示上传结果
    if (successCount > 0) {
      $baseMessage(
        `成功上传 ${successCount} 个文件${
          failCount > 0 ? `，失败 ${failCount} 个` : ''
        }`,
        successCount === files.length ? 'success' : 'warning',
        successCount === files.length
          ? 'vab-hey-message-success'
          : 'vab-hey-message-warning'
      )

      // 刷新文件列表
      loadData()
    } else {
      $baseMessage('文件上传失败', 'error', 'vab-hey-message-error')
    }
  }

  // 监听属性变化
  watch(
    [() => props.directoryId, () => props.searchKeyword],
    () => {
      // 当目录或搜索关键词变化时，重置排序、分页和高级筛选
      currentPage.value = 1
      sortField.value = 'updated_at'
      sortOrder.value = 'desc'
      advancedFilters.value = {}
      // 重置高级搜索面板
      if (advancedSearchRef.value) {
        advancedSearchRef.value.handleReset()
      }
      loadData()
    },
    { immediate: true }
  )

  // 暴露方法
  defineExpose({
    loadData,
    selectedItems,
  })
</script>

<style scoped>
  :deep() {
    .el-row {
      flex-wrap: nowrap;
    }
  }

  .unified-file-list {
    height: 100%;
  }

  .unified-file-item {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .unified-file-icon {
    flex-shrink: 0;
  }

  .unified-row-name {
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .unified-row-name:hover {
    color: #3977f3;
  }

  .unified-file-list-footer-wrapper {
    margin-top: 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .unified-file-list-stats-info {
    display: flex;
    gap: 16px;
    margin: 12px 0 0 0;
    color: #666;
    font-size: 14px;
  }

  .unified-file-list-stats-item {
    padding: 4px 8px;
    background: #f5f5f5;
    border-radius: 4px;
  }

  .unified-file-list-pagination-wrapper {
    display: flex;
    justify-content: flex-start;
  }

  /* 操作列样式 - 与 DirectoryTree 保持一致 */
  .unified-file-list__actions {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .unified-file-list__more-icon {
    padding: 2px;
    font-size: 12px;
    cursor: pointer;
  }

  /* 表格行悬停时显示操作按钮 */
  :deep(.el-table__row:hover) {
    .unified-file-list__actions {
      opacity: 1;
    }
  }

  /* 批量操作工具栏样式 */
  .unified-file-list__batch-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    margin-bottom: 16px;
    background: #f0f9ff;
    border: 1px solid #bae6fd;
    border-radius: 6px;
  }

  .unified-file-list__batch-info {
    font-size: 14px;
    color: #0369a1;
    font-weight: 500;
  }

  .unified-file-list__batch-actions {
    display: flex;
    gap: 8px;
  }

  .unified-file-list__batch-actions .el-button {
    margin: 0;
  }

  /* 拖拽上传样式 */
  .unified-file-list__table {
    position: relative;
  }

  .unified-file-list__drag-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(64, 158, 255, 0.1);
    border: 2px dashed #409eff;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    backdrop-filter: blur(2px);
  }

  .unified-file-list__drag-content {
    text-align: center;
    color: #409eff;
  }

  .unified-file-list__drag-icon {
    font-size: 48px;
    margin-bottom: 16px;
  }

  .unified-file-list__drag-text {
    font-size: 16px;
    font-weight: 500;
  }

  /* 高级搜索弹出式布局样式 */
  .unified-file-list__advanced-search-wrapper {
    margin-bottom: 16px;
  }

  /* 全局弹出层样式 */
  :global(.unified-file-list__advanced-search-popover) {
    padding: 0 !important;
  }

  :global(.unified-file-list__advanced-search-popover .el-popover__title) {
    display: none;
  }

  /* 弹出层内的高级搜索面板样式调整 */
  :global(.unified-file-list__advanced-search-popover .advanced-search-panel) {
    margin-bottom: 0;
  }

  :global(
      .unified-file-list__advanced-search-popover .advanced-search-panel__toggle
    ) {
    display: none;
  }

  :global(
      .unified-file-list__advanced-search-popover
        .advanced-search-panel__content
    ) {
    border: none;
    border-radius: 0;
    background: #fff;
  }
</style>
