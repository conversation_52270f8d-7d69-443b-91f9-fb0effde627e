<template>
  <div class="fms-breadcrumb-container">
    <!-- 导航按钮 -->
    <div class="breadcrumb-navigation">
      <el-button
        text
        :disabled="!canGoBack"
        size="small"
        :icon="Back"
        @click="handleNavigation('back')"
        title="后退"
      />
      <el-button
        text
        :disabled="!canGoForward"
        size="small"
        :icon="Right"
        @click="handleNavigation('forward')"
        title="前进"
      />
      <el-button
        text
        :disabled="!canGoUp"
        size="small"
        :icon="Top"
        @click="handleNavigation('up')"
        title="返回上层目录"
      />
    </div>

    <!-- 面包屑路径 -->
    <div class="breadcrumb-path">
      <el-breadcrumb separator="/">
        <el-breadcrumb-item
          v-for="(item, index) in items"
          :key="item.id || 'root'"
          :class="{ 'is-link': index < items.length - 1 }"
          @click="handleClick(item, index)"
        >
          <el-icon v-if="index === 0"><HomeFilled /></el-icon>
          {{ item.name }}
        </el-breadcrumb-item>
      </el-breadcrumb>
    </div>
  </div>
</template>

<script setup>
  import { HomeFilled, Back, Right, Top } from '@element-plus/icons-vue'

  const props = defineProps({
    items: {
      type: Array,
      default: () => [],
    },
    canGoBack: {
      type: Boolean,
      default: false,
    },
    canGoForward: {
      type: Boolean,
      default: false,
    },
    canGoUp: {
      type: Boolean,
      default: false,
    },
  })

  const emit = defineEmits(['change', 'navigate'])

  const handleClick = (item, index) => {
    if (index < props.items.length - 1) {
      emit('change', item)
    }
  }

  // 处理导航操作
  const handleNavigation = (action) => {
    emit('navigate', action)
  }
</script>

<style scoped lang="scss">
  .fms-breadcrumb-container {
    display: flex;
    align-items: center;
    gap: 12px;

    .breadcrumb-navigation {
      display: flex;
      flex-shrink: 0;

      .el-button {
        width: 28px;
        height: 28px;
        padding: 0;

        &:disabled {
          opacity: 0.5;
        }
      }
    }

    .breadcrumb-path {
      flex: 1;
      min-width: 0;
    }

    .is-link {
      cursor: pointer;
    }

    .is-link:hover {
      color: var(--el-color-primary);
    }
  }
</style>
