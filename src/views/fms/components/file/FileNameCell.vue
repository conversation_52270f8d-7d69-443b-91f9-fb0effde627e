<template>
  <div class="file-name-cell" @click="handleClick">
    <div class="file-name-cell__icon">
      <!-- 图片文件显示缩略图 -->
      <el-image
        v-if="isImage && showThumbnail"
        :src="thumbnailUrl"
        :alt="file.name"
        fit="cover"
        class="file-name-cell__thumbnail"
        @error="handleImageError"
      >
        <template #error>
          <div class="file-name-cell__icon-fallback">
            <!-- <vab-icon
              icon="untested"
              is-custom-svg
              class="file-name-cell__svg-icon"
            /> -->
            <el-icon class="file-name-cell__svg-icon"><Picture /></el-icon>
          </div>
        </template>
      </el-image>

      <!-- 非图片文件或图片加载失败时显示文件类型图标 -->
      <vab-icon
        v-else
        :icon="fileIcon"
        is-custom-svg
        class="file-name-cell__svg-icon"
      />
    </div>

    <div class="file-name-cell__content">
      <span
        class="file-name-cell__name"
        :class="{ 'file-name-cell__name--clickable': clickable }"
        :title="file.name"
      >
        {{ file.name }}
      </span>

      <!-- 可选的文件大小显示 -->
      <span v-if="showSize && file.size" class="file-name-cell__size">
        （{{ formatFileSize(file.size) }}）
      </span>
    </div>
  </div>
</template>

<script setup>
  import { computed, ref } from 'vue'
  import { getFileType } from '@/utils/index'

  // Props 定义
  const props = defineProps({
    // 文件对象，包含 name, mime_type, size 等字段
    file: {
      type: Object,
      required: true,
      validator: (file) => file && file.name,
    },

    // 是否可点击预览
    clickable: {
      type: Boolean,
      default: true,
    },

    // 是否显示文件大小
    showSize: {
      type: Boolean,
      default: false,
    },

    // 是否显示图片缩略图
    showThumbnail: {
      type: Boolean,
      default: true,
    },

    // 缩略图URL（可选，如果不提供则尝试从文件对象获取）
    thumbnailUrl: {
      type: String,
      default: '',
    },
  })

  // Events 定义
  const emit = defineEmits(['preview', 'click'])

  // 响应式数据
  const imageError = ref(false)

  // 计算属性
  const fileExtension = computed(() => {
    if (!props.file.name) return ''
    const parts = props.file.name.split('.')
    return parts.length > 1 ? parts.pop().toLowerCase() : ''
  })

  const mimeType = computed(() => {
    return props.file.mime_type || props.file.mimeType || ''
  })

  const isImage = computed(() => {
    return (
      mimeType.value.startsWith('image/') ||
      ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg', 'ico'].includes(
        fileExtension.value
      )
    )
  })

  const fileIcon = computed(() => {
    // 使用现有的 getFileType 函数获取文件类型
    const fileType = getFileType(mimeType.value)
    if (fileType == 'other') {
      return 'unknow_file'
    }
    return `icon-${fileType}`
  })

  const thumbnailUrl = computed(() => {
    if (!isImage.value || imageError.value) return ''

    // 优先使用传入的缩略图URL
    if (props.thumbnailUrl) return props.thumbnailUrl

    // 尝试从文件对象获取预览URL
    return (
      props.file.preview_url ||
      props.file.previewUrl ||
      props.file.download_url ||
      props.file.downloadUrl ||
      props.file.url ||
      ''
    )
  })

  // 方法
  const handleClick = () => {
    if (!props.clickable) return
    console.log('props.file:', props.file)

    emit('click', props.file)
    emit('preview', props.file)
  }

  const handleImageError = () => {
    imageError.value = true
  }

  const formatFileSize = (bytes) => {
    if (!bytes || bytes === 0) return '0 B'

    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }
</script>

<style scoped>
  .file-name-cell {
    display: flex;
    align-items: center;
    gap: 8px;
    max-width: 100%;
  }

  .file-name-cell__icon {
    flex-shrink: 0;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .file-name-cell__thumbnail {
    width: 20px;
    height: 20px;
    border-radius: 2px;
  }

  .file-name-cell__icon-fallback {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f5f5f5;
    border-radius: 2px;
  }

  .file-name-cell__svg-icon {
    width: 18px;
    height: 18px;
    min-width: 18px;
    max-height: 20px;
  }

  .file-name-cell__content {
    flex: 1;
    min-width: 0;
    display: flex;
    align-items: center;
    gap: 4px;
  }

  .file-name-cell__name {
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    font-size: 14px;
    color: #333;
  }

  .file-name-cell__name--clickable {
    cursor: pointer;
    transition: color 0.2s ease;
  }

  .file-name-cell__name--clickable:hover {
    color: var(--el-color-primary, #409eff);
  }

  .file-name-cell__size {
    flex-shrink: 0;
    font-size: 12px;
    color: #9199a3;
    white-space: nowrap;
  }

  /* 深度选择器，确保 vab-icon 正确显示 */
  :deep(.vab-icon) {
    display: flex;
    align-items: center;
    justify-content: center;
  }
</style>
