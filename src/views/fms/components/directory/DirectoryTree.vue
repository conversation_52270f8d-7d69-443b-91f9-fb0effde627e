<template>
  <div class="fms-directory-tree">
    <CreateDirectoryDialog
      v-model:visible="dialogs.createDirectory"
      :parent-id="createDirectoryParentId"
      :parent-path="createDirectoryParentPath"
      @success="onDirectoryCreated"
    />
    <CreateDirectoryDialog
      v-model:visible="dialogs.settingsDirectory"
      :directory-id="settingsDirectoryId"
      :directory-name="settingsDirectoryName"
      :is-edit-mode="true"
      @success="onDirectoryUpdated"
    />
    <div class="fms-directory-tree__header">
      <div class="fms-directory-tree__title">
        <vab-icon
          :icon="'icon-dir'"
          is-custom-svg
          :style="{
            width: 16 + 'px',
            height: 16 + 'px',
            marginRight: '6px',
          }"
        />
        <span>根目录</span>
      </div>
      <div class="fms-directory-tree__actions">
        <el-button
          type="text"
          :icon="Plus"
          size="small"
          @click="handleCommand({ action: 'create' })"
          style="margin-right: 8px"
        />
        <el-button
          type="text"
          :icon="isAllExpanded ? Fold : Expand"
          size="small"
          @click="toggleExpandAll"
          :title="isAllExpanded ? '全部收起' : '全部展开'"
          style="margin-right: 8px"
        />
        <el-button
          type="text"
          :icon="Refresh"
          size="small"
          @click="refreshTree"
        />
      </div>
    </div>

    <div
      class="fms-directory-tree__content"
      :style="{ height: height }"
      v-loading="isLoading"
    >
      <a-tree
        v-if="showTree && treeData?.length > 0"
        ref="treeRef"
        :tree-data="treeData"
        :height="treeHeight"
        :field-names="fieldNames"
        :selected-keys="selectedKeys"
        :expanded-keys="Array.from(expandedKeys)"
        :load-data="loadData"
        :draggable="draggable"
        :block-node="true"
        @select="handleSelect"
        @expand="handleExpand"
        @drop="handleDrop"
      >
        <!-- 自定义 switcherIcon -->
        <template #switcherIcon="{ expanded }">
          <el-icon
            class="custom-switcher-icon"
            :style="{
              transform: expanded ? 'rotate(90deg)' : 'rotate(0deg)',
              color: '#666666',
            }"
          >
            <ArrowRight />
          </el-icon>
        </template>
        <template #title="node">
          <div
            class="fms-directory-tree__node"
            @dblclick="handleDoubleClick(node)"
          >
            <div class="fms-directory-tree__node-content">
              <vab-icon
                :icon="'icon-dir'"
                is-custom-svg
                :style="{
                  width: 16 + 'px',
                  height: 16 + 'px',
                  marginRight: '6px',
                }"
              />
              <span class="fms-directory-tree__node-title">
                {{ node.name }}
              </span>
            </div>
            <div
              class="fms-directory-tree__node-actions"
              style="display: flex"
              @click.stop=""
              @dblclick.stop=""
            >
              <el-dropdown trigger="click" @command="handleCommand">
                <vab-icon
                  class="fms-directory-tree__node-more"
                  is-custom-svg
                  icon="more"
                />
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item
                      :command="{
                        action: 'create',
                        id: node.id,
                        name: node.name,
                        node: node,
                      }"
                      :disabled="!canCreate(node.permissions)"
                    >
                      <el-icon><Plus /></el-icon>
                      新建子目录
                    </el-dropdown-item>
                    <el-dropdown-item
                      :command="{
                        action: 'settings',
                        id: node.id,
                        name: node.name,
                        node: node,
                      }"
                      :disabled="!canSettings(node.permissions)"
                    >
                      <el-icon><Setting /></el-icon>
                      设置
                    </el-dropdown-item>
                    <el-dropdown-item
                      :command="{
                        action: 'move',
                        id: node.id,
                        name: node.name,
                        node: node,
                      }"
                      :disabled="!canMove(node.permissions)"
                    >
                      <el-icon><Rank /></el-icon>
                      移动
                    </el-dropdown-item>
                    <el-dropdown-item
                      :command="{
                        action: 'delete',
                        id: node.id,
                        name: node.name,
                        node: node,
                      }"
                      :disabled="!canDelete(node.permissions)"
                      divided
                    >
                      <el-icon><Delete /></el-icon>
                      删除
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </div>
        </template>
      </a-tree>

      <div v-else-if="!isLoading" class="fms-directory-tree__empty">
        <el-empty description="暂无目录数据" />
      </div>
    </div>
  </div>
</template>

<script setup>
  import {
    ref,
    computed,
    watch,
    onMounted,
    inject,
    defineProps,
    defineEmits,
  } from 'vue'
  import {
    Folder,
    Document,
    Refresh,
    MoreFilled,
    Plus,
    Rank,
    Delete,
    Expand,
    Fold,
    Setting,
  } from '@element-plus/icons-vue'
  import { useDirectoryStore } from '../../stores/directoryStore'
  import { useFmsPermission } from '@/utils/bmsPermission'
  import CreateDirectoryDialog from './CreateDirectoryDialog.vue'

  // Props 定义
  const props = defineProps({
    selectedId: {
      type: [Number, String],
      default: null,
    },
    rootId: {
      type: [Number, String],
      default: null,
    },
    height: {
      type: String,
      default: '100%',
    },
    draggable: {
      type: Boolean,
      default: true,
    },
  })

  // Events 定义
  const emit = defineEmits(['select', 'move', 'delete'])

  // 注入服务
  const $baseMessage = inject('$baseMessage')
  const $baseConfirm = inject('$baseConfirm')

  // Store
  const directoryStore = useDirectoryStore()
  const { FMS_PERMISSIONS, can } = useFmsPermission()

  // 响应式数据
  const treeRef = ref()
  const showTree = ref(true)
  const selectedKeys = ref([])
  const expandedKeys = ref(new Set())
  const isAllExpanded = ref(false)

  // 对话框状态
  const dialogs = ref({
    createDirectory: false,
    settingsDirectory: false,
  })

  // 创建目录的父目录ID
  const createDirectoryParentId = ref(null)

  // 创建目录的父目录路径
  const createDirectoryParentPath = ref('')

  // 设置目录的相关数据
  const settingsDirectoryId = ref(null)
  const settingsDirectoryName = ref('')

  // 计算属性
  const isLoading = computed(() => directoryStore.isTreeLoading)
  const treeData = computed(() => directoryStore.treeData)
  const treeHeight = computed(() => {
    // 计算树的实际高度
    const headerHeight = 40
    const padding = 16
    return `calc(${props.height} - ${headerHeight + padding}px)`
  })

  // 字段映射配置
  const fieldNames = {
    key: 'id',
    title: 'name',
    children: 'children',
  }

  // 组件挂载时加载目录树
  onMounted(() => {
    loadTreeData()
  })

  // 监听选中ID变化
  watch(
    () => props.selectedId,
    (newId) => {
      if (newId) {
        selectedKeys.value = [newId]
      } else {
        selectedKeys.value = []
      }
    }
  )

  // 方法
  const loadTreeData = async () => {
    try {
      await directoryStore.fetchDirectoryTree({
        rootId: props.rootId,
        includePermissions: true,
      })

      // 设置初始选中
      if (props.selectedId) {
        selectedKeys.value = [props.selectedId]
      }

      // 设置初始展开
      if (Array.isArray(treeData.value) && treeData.value.length > 0) {
        // expandedKeys.value.add(parseInt(treeData.value[0].id))
      }

      // 更新全部展开状态
      updateExpandAllState()
    } catch (error) {
      $baseMessage('加载目录树失败', 'error', 'vab-hey-message-error')
    }
  }

  const refreshTree = async () => {
    showTree.value = false
    await loadTreeData()
    showTree.value = true
    // 更新全部展开状态
    updateExpandAllState()
  }

  // 全部展开/收起功能
  const toggleExpandAll = () => {
    if (isAllExpanded.value) {
      // 收起所有节点
      expandedKeys.value.clear()
      isAllExpanded.value = false
    } else {
      // 展开所有节点
      expandAllNodes()
      isAllExpanded.value = true
    }
  }

  // 递归展开所有节点
  const expandAllNodes = (nodes = treeData.value) => {
    if (!Array.isArray(nodes)) return

    nodes.forEach((node) => {
      if (node.id) {
        expandedKeys.value.add(parseInt(node.id))
      }
      if (node.children && node.children.length > 0) {
        expandAllNodes(node.children)
      }
    })
  }

  const loadData = (treeNode) => {
    return new Promise((resolve) => {
      const { key } = treeNode

      const startTime = Date.now()

      directoryStore
        .fetchDirectories({
          parent_id: key,
          includePermissions: true,
        })
        .then((children) => {
          console.log('children:', children)
          treeNode.dataRef.children = children.data?.data.map((dir) => ({
            id: dir.id,
            name: dir.name,
            key: String(dir.id),
            title: dir.name,
            isLeaf: dir.isLeaf,
            has_children: dir.children?.length > 0,
            permissions: dir.permissions,
            children: dir.childrenCount > 0 ? [] : undefined,
          }))

          // 刷新树
          if (Array.isArray(treeData.value)) {
            treeData.value = [...treeData.value]
          }

          console.log(treeNode.dataRef)

          // 计算耗时
          const elapsed = Date.now() - startTime
          const delay = Math.max(0, 550 - elapsed)

          setTimeout(() => {
            // 手动展开当前节点
            if (!expandedKeys.value.has(key)) {
              expandedKeys.value.add(key)
            }
            resolve()
          }, delay)
        })
    })
  }

  const handleSelect = (selectedKeys, { node }) => {
    if (selectedKeys.length > 0) {
      const nodeData = {
        id: Number(node.key),
        name: node.title,
        key: node.key,
        ...node.dataRef,
      }
      emit('select', nodeData)
    }
  }

  // a-tree 处理节点展开/收起事件
  const handleExpand = (keys, { expanded, node }) => {
    if (expanded) {
      expandedKeys.value.add(parseInt(node.id))
    } else {
      removeKeysRecursively(node)
    }

    // 更新全部展开状态
    updateExpandAllState()
  }

  // 更新全部展开状态
  const updateExpandAllState = () => {
    const allNodeIds = getAllNodeIds(treeData.value)
    const expandedCount = allNodeIds.filter((id) =>
      expandedKeys.value.has(id)
    ).length
    isAllExpanded.value =
      expandedCount === allNodeIds.length && allNodeIds.length > 0
  }

  // 获取所有节点ID
  const getAllNodeIds = (nodes = []) => {
    const ids = []
    if (!Array.isArray(nodes)) return ids

    nodes.forEach((node) => {
      if (node.id) {
        ids.push(parseInt(node.id))
      }
      if (node.children && node.children.length > 0) {
        ids.push(...getAllNodeIds(node.children))
      }
    })
    return ids
  }

  // a-tree 处理双击事件
  const handleDoubleClick = (node) => {
    const nodeKey = parseInt(node.id)

    // 检查节点是否可展开（有子节点）
    if (!node.isLeaf) {
      // 如果节点已展开，则收起；如果未展开，则展开
      if (expandedKeys.value.has(nodeKey)) {
        expandedKeys.value.delete(nodeKey)
        removeKeysRecursively(node)
      } else {
        expandedKeys.value.add(nodeKey)
        // 如果节点还没有加载子数据，触发loadData
        if (!node.children || node.children.length === 0) {
          loadData(node)
        }
      }
    }
  }

  // a-tree 递归移除展开的子节点的 key 取消展开
  const removeKeysRecursively = (node) => {
    console.log('removeKeysRecursively:', expandedKeys.value)
    const removeKey = (n) => {
      expandedKeys.value.delete(parseInt(n.id)) // 删除当前节点 key
      if (n.children) {
        n.children.forEach((child) => removeKey(child)) // 递归删除子节点
      }
    }

    removeKey(node)
  }

  const handleDrop = async ({ node, dragNode, dropPosition }) => {
    if (!props.draggable) return

    try {
      const dragId = Number(dragNode.key)
      const dropId = Number(node.key)

      let newParentId = null

      if (dropPosition === 0) {
        // 拖拽到节点内部
        newParentId = dropId
      } else {
        // 拖拽到节点同级
        newParentId = node.dataRef.parentId
      }

      await directoryStore.moveDirectory(dragId, newParentId)

      $baseMessage('目录移动成功', 'success', 'vab-hey-message-success')

      // 刷新树
      await refreshTree()
    } catch (error) {
      $baseMessage('目录移动失败', 'error', 'vab-hey-message-error')
    }
  }

  const handleCommand = (command) => {
    console.log('command', command)
    const { action, id, name, node } = command
    const nodeId = Number(id)

    switch (action) {
      case 'create': {
        createDirectoryParentId.value = nodeId

        // 设置父目录路径
        if (name) {
          createDirectoryParentPath.value = name || '未知目录'
        } else {
          createDirectoryParentPath.value = '根目录'
        }
        dialogs.value.createDirectory = true
        break
      }
      case 'settings': {
        settingsDirectoryId.value = nodeId
        settingsDirectoryName.value = name || '未知目录'
        dialogs.value.settingsDirectory = true
        break
      }
      case 'move':
        emit('move', { id: nodeId })
        break
      case 'delete':
        handleDelete(nodeId)
        break
    }
  }

  const onDirectoryCreated = async () => {
    await refreshTree()
    // $baseMessage('目录创建成功', 'success', 'vab-hey-message-success')
  }

  const onDirectoryUpdated = async (updatedDirectory) => {
    // 确保清除相关缓存，解决权限数据显示不一致的问题
    if (updatedDirectory && updatedDirectory.id) {
      directoryStore.directoryCache.delete(updatedDirectory.id)
    }

    await refreshTree()
    // $baseMessage('目录设置更新成功', 'success', 'vab-hey-message-success')
  }

  const handleDelete = (nodeId) => {
    $baseConfirm(
      '确认删除该目录？删除后将移至回收站。',
      () => {},
      async () => {
        try {
          await directoryStore.deleteDirectory(nodeId)
          $baseMessage('目录删除成功', 'success', 'vab-hey-message-success')

          // 刷新树
          await refreshTree()

          emit('delete', { id: nodeId })
        } catch (error) {
          $baseMessage('目录删除失败', 'error', 'vab-hey-message-error')
        }
      }
    )
  }

  // 权限检查方法
  const canCreate = (permissions) => {
    return true
    // return (
    //   can('directory', null, FMS_PERMISSIONS.MANAGE) ||
    //   permissions & FMS_PERMISSIONS.MANAGE
    // )
  }

  const canSettings = (permissions) => {
    return true
    // return (
    //   can('directory', null, FMS_PERMISSIONS.MANAGE) ||
    //   permissions & FMS_PERMISSIONS.MANAGE
    // )
  }

  const canMove = (permissions) => {
    return true
    // return (
    //   can('directory', null, FMS_PERMISSIONS.MANAGE) ||
    //   permissions & FMS_PERMISSIONS.MANAGE
    // )
  }

  const canDelete = (permissions) => {
    return true
    // return (
    //   can('directory', null, FMS_PERMISSIONS.DELETE) ||
    //   permissions & FMS_PERMISSIONS.DELETE
    // )
  }

  // 生命周期
  onMounted(() => {
    loadTreeData()
  })
</script>

<style lang="scss" scoped>
  .fms-directory-tree {
    height: 100%;
    display: flex;
    flex-direction: column;

    &__header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 8px 0px;
      margin: 0 10px;
      border-bottom: 1px solid #dcdfe6;
    }

    &__title {
      display: flex;
      align-items: center;
      gap: 6px;
      font-size: 14px;
      font-weight: 500;
      color: var(--el-text-color-primary);
    }

    &__actions {
      display: flex;
      align-items: center;
    }

    &__content {
      flex: 1;
      overflow: auto;
      padding: 8px;
    }

    &__node {
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 100%;
      padding: 2px 4px;
      position: relative;

      // 移除节点级别的 hover 效果，因为现在由 ant-tree-treenode 处理
    }

    &__node-content {
      display: flex;
      align-items: center;
      gap: 6px;
      flex: 1;
      min-width: 0;
    }

    &__node-icon {
      font-size: 14px;
      color: var(--el-color-primary);
    }

    &__node-title {
      font-size: 13px;
      color: var(--el-text-color-primary);
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    &__node-actions {
      opacity: 0;
      transition: opacity 0.2s;
    }

    &__node-more {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 24px;
      height: 24px;
      padding: 2px;
      font-size: 12px;
      cursor: pointer;
      border-radius: 4px;
      transition: background-color 0.2s;

      &:hover {
        background-color: rgba(0, 0, 0, 0.06);
      }
    }

    &__empty {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 200px;
    }
  }

  // antd-vue 树组件样式覆盖 - 参考 WikiContent.vue 样式
  :deep(.ant-tree) {
    background: transparent;

    .ant-tree-treenode {
      padding: 0 !important; // 强制覆盖
      margin-bottom: 0; // itemHeight没有计算margin-bottom, 会导致树结构高度计算错误底下部分item无法滚动触及将不可视
      border-radius: 6px;
      transition: background-color 0.3s;

      // 整个节点的悬停效果
      &:hover {
        background-color: #ebf1fe !important;

        .fms-directory-tree__node-actions {
          opacity: 1;
        }

        // 确保在节点悬停时内容区域不要有自己的背景色
        .ant-tree-node-content-wrapper {
          background-color: transparent !important;
        }
      }

      // 选中节点时整个节点的样式
      &.ant-tree-treenode-selected,
      &:has(.ant-tree-node-selected) {
        background-color: #ebf1fe !important;
        border-radius: 6px;

        .ant-tree-node-selected {
          background-color: transparent !important;
        }
      }
    }

    .ant-tree-node-content-wrapper {
      padding: 0;
      line-height: 32px;
      background: transparent !important;
      transition: none;

      &:hover {
        background-color: transparent !important;
      }

      &.ant-tree-node-selected {
        background-color: transparent !important;
      }
    }

    .ant-tree-title {
      width: 100%;
    }

    .ant-tree-switcher {
      color: #666666;
      padding-top: 7px;
    }

    .ant-tree-indent-unit {
      width: 16px;
    }

    // 滚动条样式
    .ant-tree-list-scrollbar {
      width: 6px !important;
      opacity: 0 !important;
      transition: opacity 0.3s;
    }

    .ant-tree-list-scrollbar-thumb {
      background-color: rgba(144, 147, 153, 0.3) !important;
      border-radius: 4px !important;
    }

    .ant-tree-list-holder {
      overflow-x: clip;
    }

    .ant-tree-list:hover .ant-tree-list-scrollbar,
    .ant-tree-list.scrolling .ant-tree-list-scrollbar {
      opacity: 1 !important;
    }

    // 调整树节点内部元素与整个节点的间距
    .ant-tree-list-holder-inner {
      padding: 0 6px;
    }
  }
</style>
