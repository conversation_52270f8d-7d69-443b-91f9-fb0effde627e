<template>
  <div class="advanced-search-panel">
    <!-- 搜索面板切换按钮 -->
    <div class="advanced-search-panel__toggle">
      <el-button
        type="text"
        :icon="expanded ? ArrowUp : ArrowDown"
        @click="togglePanel"
        class="advanced-search-panel__toggle-btn"
      >
        {{ expanded ? '收起筛选' : '高级筛选' }}
      </el-button>
    </div>

    <!-- 筛选面板内容 -->
    <el-collapse-transition>
      <div v-show="expanded" class="advanced-search-panel__content">
        <el-form
          :model="searchForm"
          label-width="80px"
          class="advanced-search-panel__form"
        >
          <!-- 文件格式筛选 -->
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="文件格式">
                <el-select
                  v-model="searchForm.fileTypes"
                  multiple
                  placeholder="选择文件格式"
                  collapse-tags
                  collapse-tags-tooltip
                  style="width: 100%"
                >
                  <el-option-group
                    v-for="group in fileTypeGroups"
                    :key="group.label"
                    :label="group.label"
                  >
                    <el-option
                      v-for="item in group.options"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-option-group>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <!-- 文件大小范围 -->
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="文件大小">
                <div class="advanced-search-panel__size-range">
                  <el-input
                    v-model="searchForm.sizeMin"
                    placeholder="最小"
                    type="number"
                    min="0"
                    style="width: 36%"
                  />
                  <span class="advanced-search-panel__size-separator">-</span>
                  <el-input
                    v-model="searchForm.sizeMax"
                    placeholder="最大"
                    type="number"
                    min="0"
                    style="width: 36%"
                  />
                  <el-select v-model="searchForm.sizeUnit" style="width: 28%">
                    <el-option label="B" value="B" />
                    <el-option label="KB" value="KB" />
                    <el-option label="MB" value="MB" />
                    <el-option label="GB" value="GB" />
                  </el-select>
                </div>
              </el-form-item>
            </el-col>
          </el-row>

          <!-- 时间范围筛选 -->
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="时间范围">
                <el-select
                  v-model="searchForm.timeType"
                  style="width: 100%; margin-bottom: 8px"
                >
                  <el-option label="创建时间" value="created_at" />
                  <el-option label="修改时间" value="updated_at" />
                </el-select>
                <el-date-picker
                  v-model="searchForm.timeRange"
                  type="datetimerange"
                  range-separator="至"
                  start-placeholder="开始时间"
                  end-placeholder="结束时间"
                  format="YYYY-MM-DD HH:mm"
                  value-format="YYYY-MM-DD HH:mm:ss"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <!-- 可见性筛选 -->
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="可见性">
                <el-select
                  v-model="searchForm.visibility"
                  multiple
                  placeholder="选择可见性"
                  style="width: 100%"
                >
                  <el-option label="公开" value="public" />
                  <el-option label="部门" value="department" />
                  <el-option label="用户" value="user" />
                  <el-option label="私有" value="private" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <!-- 操作按钮 -->
          <el-row>
            <el-col :span="24">
              <div class="advanced-search-panel__actions">
                <el-button
                  type="primary"
                  @click="handleSearch"
                  :loading="searching"
                >
                  搜索
                </el-button>
                <el-button @click="handleReset">重置</el-button>
                <!-- <el-button type="text" @click="togglePanel">收起</el-button> -->
              </div>
            </el-col>
          </el-row>
        </el-form>
      </div>
    </el-collapse-transition>
  </div>
</template>

<script setup>
  import { ref, reactive, computed } from 'vue'
  import { ArrowUp, ArrowDown } from '@element-plus/icons-vue'

  // Props 定义
  const props = defineProps({
    // 是否默认展开
    defaultExpanded: {
      type: Boolean,
      default: false,
    },
    // 搜索状态
    searching: {
      type: Boolean,
      default: false,
    },
  })

  // Events 定义
  const emit = defineEmits(['search', 'reset'])

  // 响应式数据
  const expanded = ref(props.defaultExpanded)

  // 搜索表单数据
  const searchForm = reactive({
    fileTypes: [], // 文件格式
    sizeMin: '', // 最小文件大小
    sizeMax: '', // 最大文件大小
    sizeUnit: 'MB', // 文件大小单位
    timeType: 'updated_at', // 时间类型
    timeRange: [], // 时间范围
    visibility: [], // 可见性
  })

  // 文件格式分组选项
  const fileTypeGroups = [
    {
      label: '文档',
      options: [
        { label: 'PDF', value: 'application/pdf' },
        {
          label: 'Word',
          value:
            'application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        },
        {
          label: 'Excel',
          value:
            'application/vnd.ms-excel,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        },
        {
          label: 'PowerPoint',
          value:
            'application/vnd.ms-powerpoint,application/vnd.openxmlformats-officedocument.presentationml.presentation',
        },
        {
          label: '文本',
          value: 'text/plain,text/html,text/css,text/javascript',
        },
      ],
    },
    {
      label: '图片',
      options: [
        { label: 'JPEG', value: 'image/jpeg' },
        { label: 'PNG', value: 'image/png' },
        { label: 'GIF', value: 'image/gif' },
        { label: 'WebP', value: 'image/webp' },
        { label: 'SVG', value: 'image/svg+xml' },
      ],
    },
    {
      label: '视频',
      options: [
        { label: 'MP4', value: 'video/mp4' },
        { label: 'WebM', value: 'video/webm' },
        { label: 'AVI', value: 'video/avi' },
        { label: 'MOV', value: 'video/quicktime' },
      ],
    },
    {
      label: '音频',
      options: [
        { label: 'MP3', value: 'audio/mpeg' },
        { label: 'WAV', value: 'audio/wav' },
        { label: 'OGG', value: 'audio/ogg' },
        { label: 'AAC', value: 'audio/aac' },
      ],
    },
    {
      label: '压缩包',
      options: [
        { label: 'ZIP', value: 'application/zip' },
        { label: 'RAR', value: 'application/x-rar-compressed' },
        { label: '7Z', value: 'application/x-7z-compressed' },
        { label: 'TAR', value: 'application/x-tar' },
      ],
    },
  ]

  // 方法
  const togglePanel = () => {
    expanded.value = !expanded.value
  }

  const handleSearch = () => {
    // 构建搜索参数
    const searchParams = buildSearchParams()
    emit('search', searchParams)
  }

  const handleReset = () => {
    // 重置表单
    Object.assign(searchForm, {
      fileTypes: [],
      sizeMin: '',
      sizeMax: '',
      sizeUnit: 'MB',
      timeType: 'updated_at',
      timeRange: [],
      visibility: [],
    })

    emit('reset')
  }

  // 构建搜索参数
  const buildSearchParams = () => {
    const params = {}

    // 文件格式筛选
    if (searchForm.fileTypes.length > 0) {
      // 将多个MIME类型合并
      const mimeTypes = searchForm.fileTypes.flatMap((type) => type.split(','))
      params.mime_types = mimeTypes
    }

    // 文件大小筛选
    if (searchForm.sizeMin || searchForm.sizeMax) {
      const unit = searchForm.sizeUnit
      const multiplier =
        {
          B: 1,
          KB: 1024,
          MB: 1024 * 1024,
          GB: 1024 * 1024 * 1024,
        }[unit] || 1

      if (searchForm.sizeMin) {
        params.size_min = parseInt(searchForm.sizeMin) * multiplier
      }
      if (searchForm.sizeMax) {
        params.size_max = parseInt(searchForm.sizeMax) * multiplier
      }
    }

    // 时间范围筛选
    if (searchForm.timeRange && searchForm.timeRange.length === 2) {
      params.time_type = searchForm.timeType
      params.time_start = searchForm.timeRange[0]
      params.time_end = searchForm.timeRange[1]
    }

    // 可见性筛选
    if (searchForm.visibility.length > 0) {
      params.visibility = searchForm.visibility
    }

    return params
  }

  // 暴露方法
  defineExpose({
    togglePanel,
    handleReset,
    searchForm,
  })
</script>

<style scoped>
  .advanced-search-panel {
    margin-bottom: 16px;
  }

  .advanced-search-panel__toggle {
    text-align: right;
    margin-bottom: 8px;
  }

  .advanced-search-panel__toggle-btn {
    font-size: 14px;
    padding: 4px 8px;
  }

  .advanced-search-panel__content {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 20px;
  }

  .advanced-search-panel__form {
    margin: 0;
  }

  .advanced-search-panel__size-range {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .advanced-search-panel__size-separator {
    color: #909399;
    font-size: 14px;
  }

  .advanced-search-panel__actions {
    text-align: center;
    padding-top: 16px;
    border-top: 1px solid #e9ecef;
  }

  .advanced-search-panel__actions .el-button {
    margin: 0 8px;
  }

  /* 深度选择器样式 */
  :deep(.el-form-item) {
    margin-bottom: 20px;
  }

  :deep(.el-form-item__label) {
    font-weight: 500;
    color: #333;
  }

  /* 每行筛选项之间的间距 */
  :deep(.el-row) {
    margin-bottom: 8px;
  }

  :deep(.el-row:last-child) {
    margin-bottom: 0;
  }
</style>
