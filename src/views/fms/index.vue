<template>
  <div class="fms-main">
    <div class="fms-main__container">
      <div class="fms-split-container" ref="splitContainerRef">
        <!-- 左侧目录树面板 -->
        <div
          class="fms-left-panel"
          ref="leftPanelRef"
          :style="{
            width: leftPanelWidth + 'px',
            minWidth: '280px',
            maxWidth: '600px',
          }"
        >
          <div
            style="
              display: flex;
              align-items: center;
              height: 60px;
              margin: 0px 10px;
              font-size: 16px;
              font-weight: 700;
              color: black;
              border-bottom: 1px solid #dcdfe6;
            "
          >
            <span>FMS模块</span>
          </div>
          <DirectoryTree
            :selected-id="currentDirectoryId"
            :height="treeHeight"
            @select="onDirectorySelect"
          />
        </div>

        <!-- 右侧内容面板 -->
        <div
          class="fms-right-panel"
          ref="rightPanelRef"
          style="flex: 1; min-width: 680px; overflow: hidden"
        >
          <div class="fms-main__header">
            <Breadcrumb
              :items="breadcrumbItems"
              :can-go-back="navigationState.canGoBack"
              :can-go-forward="navigationState.canGoForward"
              :can-go-up="navigationState.canGoUp"
              @change="onBreadcrumbChange"
              @navigate="handleBreadcrumbNavigation"
            />
            <div class="fms-main__toolbar">
              <div class="fms-main__search">
                <el-input
                  v-model="searchKeyword"
                  placeholder="搜索文件和目录"
                  :prefix-icon="Search"
                  @keyup.enter="onSearch"
                  clearable
                />
              </div>
            </div>
          </div>
          <div class="fms-main__main">
            <!-- 统一文件和目录列表 -->
            <UnifiedFileList
              ref="unifiedFileListRef"
              :directory-id="currentDirectoryId"
              :search-keyword="searchKeyword"
              @refresh="refreshData"
              @directory-enter="onDirectoryEnter"
              @file-preview="onFilePreview"
              @item-rename="onItemRename"
              @item-delete="onItemDelete"
              @item-move="onItemMove"
              @upload="handleUpload"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- 对话框组件 -->
    <CreateDirectoryDialog
      v-model:visible="dialogs.createDirectory"
      :parent-id="currentDirectoryId"
      @success="onDirectoryCreated"
    />

    <RenameDirectoryDialog
      v-model:visible="dialogs.renameDirectory"
      :id="selectedDirectory?.id"
      :name="selectedDirectory?.name"
      @success="onDirectoryRenamed"
    />

    <MoveDirectoryDialog
      v-model:visible="dialogs.moveDirectory"
      :id="selectedDirectory?.id"
      :target-parent-id="selectedDirectory?.parentId"
      @success="onDirectoryMoved"
    />

    <RenameDialog
      v-model="dialogs.rename"
      :item="selectedItem"
      @success="onItemRenamed"
    />

    <MoveDialog
      v-model="dialogs.move"
      :item="selectedItem"
      @success="onItemMoved"
    />

    <FileUpload
      v-model="dialogs.fileUpload"
      :directory-id="currentDirectoryId"
      @success="onFileUploaded"
    />

    <FilePreview
      :visible="!!previewFile"
      :file="previewFile"
      @update:visible="closeFilePreview"
    />
  </div>
</template>

<script setup>
  import {
    ref,
    reactive,
    computed,
    onMounted,
    onUnmounted,
    nextTick,
    inject,
  } from 'vue'
  import Split from 'split.js'
  import { useFmsStore } from './stores/fmsStore'
  import { useDirectoryStore } from './stores/directoryStore'
  import { useFileStore } from './stores/fileStore'

  // Element Plus 图标
  import { Upload, Search } from '@element-plus/icons-vue'

  // 通用组件
  import Breadcrumb from './components/Breadcrumb.vue'

  // 目录组件
  import DirectoryTree from './components/directory/DirectoryTree.vue'
  import CreateDirectoryDialog from './components/directory/CreateDirectoryDialog.vue'
  import RenameDirectoryDialog from './components/directory/RenameDirectoryDialog.vue'
  import MoveDirectoryDialog from './components/directory/MoveDirectoryDialog.vue'

  // 文件组件
  import UnifiedFileList from './components/UnifiedFileList.vue'
  import FilePreview from './components/file/FilePreview.vue'
  import FileUpload from './components/FileUpload.vue'
  import RenameDialog from './components/RenameDialog.vue'
  import MoveDialog from './components/MoveDialog.vue'

  // 注入基础服务
  const $baseMessage = inject('$baseMessage')
  const $baseConfirm = inject('$baseConfirm')

  // Split.js 相关引用
  const splitContainerRef = ref(null)
  const leftPanelRef = ref(null)
  const rightPanelRef = ref(null)
  const splitInstance = ref(null)

  // 左侧面板宽度
  const leftPanelWidth = ref(300)

  // 响应式数据
  const searchKeyword = ref('')
  const currentDirectoryId = ref(null)
  const selectedDirectory = ref(null)
  const selectedItem = ref(null)
  const selectedFile = ref(null)
  const previewFile = ref(null)
  const treeHeight = ref('calc(100vh - 200px)')
  const unifiedFileListRef = ref(null)

  // 导航历史记录管理
  const navigationHistory = ref([])
  const currentHistoryIndex = ref(-1)

  // 对话框状态
  const dialogs = reactive({
    createDirectory: false,
    renameDirectory: false,
    moveDirectory: false,
    rename: false,
    move: false,
    fileUpload: false,
    filePreview: false,
  })

  // 面包屑路径数据
  const breadcrumbItems = ref([{ name: '根目录', id: null }])

  // 更新面包屑路径
  const updateBreadcrumb = async () => {
    const items = [{ name: '根目录', id: null }]

    if (currentDirectoryId.value) {
      const directoryStore = useDirectoryStore()

      try {
        // 使用异步方法获取完整路径
        const pathItems = await directoryStore.getDirectoryPath(
          currentDirectoryId.value
        )

        if (pathItems && pathItems.length > 0) {
          items.push(...pathItems)
        } else {
          // 如果无法获取完整路径，至少显示当前目录
          items.push({ name: '当前目录', id: currentDirectoryId.value })
        }
      } catch (error) {
        console.error('Failed to get directory path:', error)
        // 出错时也显示当前目录
        items.push({ name: '当前目录', id: currentDirectoryId.value })
      }
    }

    breadcrumbItems.value = items
  }

  // 导航状态计算属性
  const navigationState = computed(() => {
    return {
      canGoBack: currentHistoryIndex.value > 0,
      canGoForward:
        currentHistoryIndex.value < navigationHistory.value.length - 1,
      canGoUp: breadcrumbItems.value.length > 1, // 当不在根目录时可以返回上层
    }
  })

  // 方法
  const onBreadcrumbChange = async (item) => {
    currentDirectoryId.value = item.id
    addToHistory(item.id)
    await updateBreadcrumb()
    refreshData()
  }

  const onSearch = () => {
    refreshData()
  }

  const onDirectorySelect = async (node) => {
    currentDirectoryId.value = node.id
    addToHistory(node.id)
    await updateBreadcrumb()
    refreshData()
  }

  const refreshData = () => {
    // 统一文件列表组件会自动刷新数据
    if (unifiedFileListRef.value) {
      unifiedFileListRef.value.loadData()
    }
  }

  // 工具栏操作
  const handleUpload = () => {
    dialogs.fileUpload = true
  }

  const handleRefresh = () => {
    refreshData()
  }

  // 统一文件列表事件处理
  const onDirectoryEnter = async (directory) => {
    currentDirectoryId.value = directory.id
    addToHistory(directory.id)
    await updateBreadcrumb()
  }

  const onFilePreview = (file) => {
    selectedFile.value = file
    previewFile.value = file
    dialogs.filePreview = true
  }

  const onItemRename = (item) => {
    selectedItem.value = item
    if (item.type === 'directory') {
      selectedDirectory.value = item
      dialogs.renameDirectory = true
    } else {
      dialogs.rename = true
    }
  }

  const onItemDelete = (item) => {
    // 删除操作已在UnifiedFileList组件中处理
    refreshData()
  }

  const onItemMove = (item) => {
    selectedItem.value = item
    if (item.type === 'directory') {
      selectedDirectory.value = item
      dialogs.moveDirectory = true
    } else {
      dialogs.move = true
    }
  }

  // 对话框事件处理
  const onDirectoryCreated = () => {
    dialogs.createDirectory = false
    refreshData()
  }

  const onDirectoryRenamed = () => {
    dialogs.renameDirectory = false
    selectedDirectory.value = null
    refreshData()
    $baseMessage('目录重命名成功', 'success', 'vab-hey-message-success')
  }

  const onDirectoryMoved = () => {
    dialogs.moveDirectory = false
    selectedDirectory.value = null
    refreshData()
    $baseMessage('目录移动成功', 'success', 'vab-hey-message-success')
  }

  const onItemRenamed = () => {
    dialogs.rename = false
    selectedItem.value = null
    refreshData()
    $baseMessage('重命名成功', 'success', 'vab-hey-message-success')
  }

  const onItemMoved = () => {
    dialogs.move = false
    selectedItem.value = null
    refreshData()
    $baseMessage('移动成功', 'success', 'vab-hey-message-success')
  }

  const onFileUploaded = () => {
    dialogs.fileUpload = false
    refreshData()
  }

  const closeFilePreview = (visible) => {
    if (!visible) {
      previewFile.value = null
      dialogs.filePreview = false
    }
  }

  // 导航历史记录管理方法
  const addToHistory = (directoryId) => {
    // 如果当前不在历史记录的最后，则删除当前位置之后的所有记录
    if (currentHistoryIndex.value < navigationHistory.value.length - 1) {
      navigationHistory.value = navigationHistory.value.slice(
        0,
        currentHistoryIndex.value + 1
      )
    }

    // 避免重复添加相同的目录ID
    if (navigationHistory.value[currentHistoryIndex.value] !== directoryId) {
      navigationHistory.value.push(directoryId)
      currentHistoryIndex.value = navigationHistory.value.length - 1
    }
  }

  // 处理面包屑导航事件
  const handleBreadcrumbNavigation = (action) => {
    if (action === 'back') {
      goBack()
    } else if (action === 'forward') {
      goForward()
    } else if (action === 'up') {
      goUp()
    }
  }

  // 后退导航
  const goBack = async () => {
    if (currentHistoryIndex.value > 0) {
      currentHistoryIndex.value--
      currentDirectoryId.value =
        navigationHistory.value[currentHistoryIndex.value]
      await updateBreadcrumb()
      refreshData()
    }
  }

  // 前进导航
  const goForward = async () => {
    if (currentHistoryIndex.value < navigationHistory.value.length - 1) {
      currentHistoryIndex.value++
      currentDirectoryId.value =
        navigationHistory.value[currentHistoryIndex.value]
      await updateBreadcrumb()
      refreshData()
    }
  }

  // 返回上层目录
  const goUp = async () => {
    if (breadcrumbItems.value.length > 1) {
      // 获取上层目录的ID（倒数第二个面包屑项）
      const parentItem = breadcrumbItems.value[breadcrumbItems.value.length - 2]
      currentDirectoryId.value = parentItem.id
      addToHistory(parentItem.id)
      await updateBreadcrumb()
      refreshData()
    }
  }

  // ===== Split.js 相关函数 =====

  // 初始化 Split.js
  const initializeSplit = () => {
    if (!leftPanelRef.value || !rightPanelRef.value) return

    // 从 localStorage 恢复左侧面板宽度
    const savedWidth = localStorage.getItem('fms_left_panel_width')
    if (savedWidth) {
      leftPanelWidth.value = parseInt(savedWidth)
    }

    // 计算初始百分比
    const containerWidth =
      splitContainerRef.value?.offsetWidth || window.innerWidth
    const leftPercent = (leftPanelWidth.value / containerWidth) * 100
    const rightPercent = 100 - leftPercent

    splitInstance.value = Split([leftPanelRef.value, rightPanelRef.value], {
      sizes: [leftPercent, rightPercent],
      minSize: [280, 680],
      maxSize: [600, Infinity],
      gutterSize: 1,
      cursor: 'col-resize',
      direction: 'horizontal',
      onDragEnd: (sizes) => {
        // 计算新的像素宽度
        const containerWidth =
          splitContainerRef.value?.offsetWidth || window.innerWidth
        const newWidth = Math.round((sizes[0] / 100) * containerWidth)
        leftPanelWidth.value = newWidth

        // 保存到 localStorage
        localStorage.setItem('fms_left_panel_width', newWidth.toString())
      },
    })
  }

  // 销毁 Split.js
  const destroySplit = () => {
    if (splitInstance.value) {
      splitInstance.value.destroy()
      splitInstance.value = null
    }
  }

  // 处理窗口大小变化
  const handleResize = () => {
    // 更新 Split.js 的大小
    if (splitInstance.value) {
      // 重新计算分割比例，保持左侧面板宽度不变
      const containerWidth =
        splitContainerRef.value?.offsetWidth || window.innerWidth
      const leftPercent = (leftPanelWidth.value / containerWidth) * 100
      const rightPercent = 100 - leftPercent

      splitInstance.value.setSizes([leftPercent, rightPercent])
    }
  }

  // 生命周期
  onMounted(async () => {
    // 初始化导航历史记录
    addToHistory(currentDirectoryId.value)
    await updateBreadcrumb()
    refreshData()

    // 初始化 Split.js（在 DOM 渲染完成后）
    await nextTick()
    initializeSplit()

    // 添加窗口大小变化监听器
    window.addEventListener('resize', handleResize)
  })

  onUnmounted(() => {
    // 销毁 Split.js 实例
    destroySplit()

    // 移除窗口大小变化监听器
    window.removeEventListener('resize', handleResize)
  })
</script>

<style lang="scss" scoped>
  .fms-main {
    height: 100vh;

    &__container {
      height: 100%;
    }

    &__header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: 60px;
      padding: 0 16px;
      border-bottom: 1px solid #dcdfe6;
      background: var(--el-bg-color);
    }

    &__toolbar {
      display: flex;
      align-items: center;
      gap: 12px;
    }

    &__search {
      width: 300px;
    }

    &__main {
      height: calc(100vh - 60px);
      padding: 16px;
      overflow: auto;
    }
  }

  // Split.js 相关样式
  .fms-split-container {
    display: flex;
    height: 100vh;
    width: 100%;
  }

  .fms-left-panel,
  .fms-right-panel {
    height: 100%;
    overflow: hidden;
  }

  .fms-left-panel {
    border-right: 1px solid var(--el-border-color-light);
    background: var(--el-bg-color);
  }

  // Split.js gutter 样式优化
  :deep(.gutter) {
    position: relative;
    background-color: transparent;
    background-repeat: no-repeat;
    background-position: 50%;
    cursor: col-resize;
    transition: all 0.2s ease;

    // 使用伪元素创建可视化的分割线
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 50%;
      transform: translateX(-50%);
      width: 1px;
      height: 100%;
      background-color: var(--el-border-color-light);
      transition: all 0.2s ease;
    }

    // 使用伪元素扩大可点击区域
    &::after {
      content: '';
      position: absolute;
      top: 0;
      left: 50%;
      transform: translateX(-50%);
      width: 4px; // 扩大可点击区域到8px
      height: 100%;
      background-color: transparent;
    }

    // 悬停效果
    &:hover {
      &::before {
        width: 1px;
        background-color: #dcdfe6;
        box-shadow: 0 0 4px rgba(64, 158, 255, 0.1);
      }
    }

    // 拖拽时的效果
    &:active {
      &::before {
        width: 3px;
        background-color: #dcdfe6;
        box-shadow: 0 0 6px rgba(64, 158, 255, 0.1);
      }
    }
  }
</style>
