<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>面包屑导航测试</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: #f5f5f5;
        }
        
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .breadcrumb-container {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 12px;
            background: #f9f9f9;
            border-radius: 4px;
            margin-bottom: 20px;
        }
        
        .navigation-buttons {
            display: flex;
            flex-shrink: 0;
            gap: 4px;
        }
        
        .nav-button {
            width: 28px;
            height: 28px;
            border: 1px solid #dcdfe6;
            background: white;
            border-radius: 4px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
        }
        
        .nav-button:hover:not(:disabled) {
            background: #f0f0f0;
            border-color: #409eff;
        }
        
        .nav-button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }
        
        .breadcrumb-path {
            flex: 1;
            min-width: 0;
        }
        
        .breadcrumb-item {
            display: inline;
            color: #606266;
        }
        
        .breadcrumb-item.is-link {
            color: #409eff;
            cursor: pointer;
        }
        
        .breadcrumb-item.is-link:hover {
            text-decoration: underline;
        }
        
        .breadcrumb-separator {
            margin: 0 8px;
            color: #c0c4cc;
        }
        
        .current-state {
            background: #e6f7ff;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
        
        .test-actions {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }
        
        .test-button {
            padding: 8px 16px;
            background: #409eff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        
        .test-button:hover {
            background: #337ecc;
        }
        
        .log {
            background: #f0f0f0;
            padding: 10px;
            border-radius: 4px;
            max-height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>面包屑导航功能测试</h1>
        
        <div class="current-state">
            <strong>当前状态：</strong><br>
            当前目录ID: <span id="currentDir">null</span><br>
            历史记录索引: <span id="historyIndex">0</span><br>
            历史记录: <span id="historyList">[]</span>
        </div>
        
        <!-- 面包屑导航组件模拟 -->
        <div class="breadcrumb-container">
            <div class="navigation-buttons">
                <button class="nav-button" id="backBtn" title="后退">←</button>
                <button class="nav-button" id="forwardBtn" title="前进">→</button>
                <button class="nav-button" id="upBtn" title="返回上层目录">↑</button>
            </div>
            <div class="breadcrumb-path" id="breadcrumbPath">
                <!-- 面包屑路径将在这里动态生成 -->
            </div>
        </div>
        
        <div class="test-actions">
            <button class="test-button" onclick="simulateEnterDirectory('dir1', '子目录1')">进入子目录1</button>
            <button class="test-button" onclick="simulateEnterDirectory('dir2', '子目录2')">进入子目录2</button>
            <button class="test-button" onclick="simulateEnterDirectory('dir3', '深层目录')">进入深层目录</button>
            <button class="test-button" onclick="goToRoot()">返回根目录</button>
        </div>
        
        <div class="log" id="log"></div>
    </div>

    <script>
        // 模拟状态
        let currentDirectoryId = null;
        let navigationHistory = [null];
        let currentHistoryIndex = 0;
        let breadcrumbItems = [{ name: '根目录', id: null }];
        
        // 模拟目录结构
        const directories = {
            'dir1': { name: '子目录1', parentId: null },
            'dir2': { name: '子目录2', parentId: 'dir1' },
            'dir3': { name: '深层目录', parentId: 'dir2' }
        };
        
        function log(message) {
            const logElement = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logElement.innerHTML += `[${timestamp}] ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
        }
        
        function updateUI() {
            document.getElementById('currentDir').textContent = currentDirectoryId || 'null';
            document.getElementById('historyIndex').textContent = currentHistoryIndex;
            document.getElementById('historyList').textContent = JSON.stringify(navigationHistory);
            
            // 更新按钮状态
            document.getElementById('backBtn').disabled = currentHistoryIndex <= 0;
            document.getElementById('forwardBtn').disabled = currentHistoryIndex >= navigationHistory.length - 1;
            document.getElementById('upBtn').disabled = breadcrumbItems.length <= 1;
            
            // 更新面包屑路径
            updateBreadcrumbPath();
        }
        
        function updateBreadcrumbPath() {
            const pathElement = document.getElementById('breadcrumbPath');
            pathElement.innerHTML = '';
            
            breadcrumbItems.forEach((item, index) => {
                const span = document.createElement('span');
                span.className = 'breadcrumb-item' + (index < breadcrumbItems.length - 1 ? ' is-link' : '');
                span.textContent = item.name;
                
                if (index < breadcrumbItems.length - 1) {
                    span.onclick = () => navigateToItem(item);
                }
                
                pathElement.appendChild(span);
                
                if (index < breadcrumbItems.length - 1) {
                    const separator = document.createElement('span');
                    separator.className = 'breadcrumb-separator';
                    separator.textContent = '/';
                    pathElement.appendChild(separator);
                }
            });
        }
        
        function addToHistory(directoryId) {
            if (currentHistoryIndex < navigationHistory.length - 1) {
                navigationHistory = navigationHistory.slice(0, currentHistoryIndex + 1);
            }
            
            if (navigationHistory[currentHistoryIndex] !== directoryId) {
                navigationHistory.push(directoryId);
                currentHistoryIndex = navigationHistory.length - 1;
            }
        }
        
        function updateBreadcrumb() {
            const items = [{ name: '根目录', id: null }];
            
            if (currentDirectoryId) {
                // 构建路径
                let current = currentDirectoryId;
                const path = [];
                
                while (current && directories[current]) {
                    path.unshift(directories[current]);
                    current = directories[current].parentId;
                }
                
                path.forEach(dir => {
                    items.push({ name: dir.name, id: Object.keys(directories).find(key => directories[key] === dir) });
                });
            }
            
            breadcrumbItems = items;
        }
        
        function simulateEnterDirectory(dirId, dirName) {
            currentDirectoryId = dirId;
            addToHistory(dirId);
            updateBreadcrumb();
            updateUI();
            log(`进入目录: ${dirName} (${dirId})`);
        }
        
        function navigateToItem(item) {
            currentDirectoryId = item.id;
            addToHistory(item.id);
            updateBreadcrumb();
            updateUI();
            log(`通过面包屑导航到: ${item.name} (${item.id})`);
        }
        
        function goBack() {
            if (currentHistoryIndex > 0) {
                currentHistoryIndex--;
                currentDirectoryId = navigationHistory[currentHistoryIndex];
                updateBreadcrumb();
                updateUI();
                log(`后退到: ${currentDirectoryId || '根目录'}`);
            }
        }
        
        function goForward() {
            if (currentHistoryIndex < navigationHistory.length - 1) {
                currentHistoryIndex++;
                currentDirectoryId = navigationHistory[currentHistoryIndex];
                updateBreadcrumb();
                updateUI();
                log(`前进到: ${currentDirectoryId || '根目录'}`);
            }
        }
        
        function goUp() {
            if (breadcrumbItems.length > 1) {
                const parentItem = breadcrumbItems[breadcrumbItems.length - 2];
                currentDirectoryId = parentItem.id;
                addToHistory(parentItem.id);
                updateBreadcrumb();
                updateUI();
                log(`返回上层目录: ${parentItem.name} (${parentItem.id})`);
            }
        }
        
        function goToRoot() {
            currentDirectoryId = null;
            addToHistory(null);
            updateBreadcrumb();
            updateUI();
            log('返回根目录');
        }
        
        // 绑定事件
        document.getElementById('backBtn').onclick = goBack;
        document.getElementById('forwardBtn').onclick = goForward;
        document.getElementById('upBtn').onclick = goUp;
        
        // 初始化
        updateUI();
        log('面包屑导航测试页面已加载');
    </script>
</body>
</html>
